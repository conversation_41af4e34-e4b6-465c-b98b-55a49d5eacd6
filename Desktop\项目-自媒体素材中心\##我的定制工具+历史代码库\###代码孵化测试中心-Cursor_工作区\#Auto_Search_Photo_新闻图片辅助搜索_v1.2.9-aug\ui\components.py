"""
现代化UI组件
Mac风格的自定义组件库
"""

from typing import Optional, List, Callable, Any
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt6.QtGui import QPainter, QColor, QPen, QBrush, QFont, QPixmap, QPainterPath
from PyQt6.QtWidgets import (
    QWidget, QPushButton, QLabel, QVBoxLayout, QHBoxLayout, 
    QProgressBar, QFrame, QGraphicsDropShadowEffect, QListWidget,
    QListWidgetItem, QTextEdit, QLineEdit, QComboBox, QGroupBox
)

from .themes import theme_manager


class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text: str = "", button_type: str = "default", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self._setup_button()
        self._setup_animations()
    
    def _setup_button(self):
        """设置按钮样式"""
        self.setMinimumHeight(36)
        self.setFont(theme_manager.current_theme.get_font(weight=QFont.Weight.Medium))
        
        # 设置属性用于样式表选择器
        if self.button_type == "primary":
            self.setProperty("primary", True)
        elif self.button_type == "danger":
            self.setProperty("danger", True)
        elif self.button_type == "success":
            self.setProperty("success", True)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(8)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
    
    def _setup_animations(self):
        """设置动画效果"""
        self.hover_animation = theme_manager.create_animation(self, "geometry")
        self.press_animation = theme_manager.create_animation(self, "geometry")
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        # 可以添加悬停动画效果
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        # 可以添加离开动画效果


class ModernProgressBar(QProgressBar):
    """现代化进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_progress_bar()
    
    def _setup_progress_bar(self):
        """设置进度条样式"""
        self.setMinimumHeight(8)
        self.setMaximumHeight(8)
        self.setTextVisible(False)
        
        # 添加圆角和阴影
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border-radius: 4px;
                border: none;
            }}
            QProgressBar::chunk {{
                background-color: {theme_manager.current_theme.ACCENT_BLUE};
                border-radius: 4px;
            }}
        """)


class NotificationWidget(QWidget):
    """通知组件"""
    
    def __init__(self, message: str, notification_type: str = "info", parent=None):
        super().__init__(parent)
        self.message = message
        self.notification_type = notification_type
        self._setup_notification()
        self._setup_animations()
    
    def _setup_notification(self):
        """设置通知样式"""
        self.setFixedHeight(60)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        
        # 图标标签
        icon_label = QLabel()
        icon_label.setFixedSize(24, 24)
        
        # 消息标签
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setFont(theme_manager.current_theme.get_font())
        
        layout.addWidget(icon_label)
        layout.addWidget(message_label, 1)
        
        # 设置背景色
        color_map = {
            "info": theme_manager.current_theme.INFO,
            "success": theme_manager.current_theme.SUCCESS,
            "warning": theme_manager.current_theme.WARNING,
            "error": theme_manager.current_theme.ERROR
        }
        
        bg_color = color_map.get(self.notification_type, theme_manager.current_theme.INFO)
        self.setStyleSheet(f"""
            NotificationWidget {{
                background-color: {bg_color};
                border-radius: {theme_manager.current_theme.RADIUS_MEDIUM}px;
                color: white;
            }}
        """)
        
        # 添加阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
    
    def _setup_animations(self):
        """设置动画"""
        self.fade_in_animation = theme_manager.create_animation(self, "windowOpacity")
        self.fade_out_animation = theme_manager.create_animation(self, "windowOpacity")
        
        # 自动隐藏定时器
        self.auto_hide_timer = QTimer()
        self.auto_hide_timer.timeout.connect(self.hide_notification)
        self.auto_hide_timer.setSingleShot(True)
    
    def show_notification(self, duration: int = 3000):
        """显示通知"""
        self.setWindowOpacity(0)
        self.show()
        
        self.fade_in_animation.setStartValue(0)
        self.fade_in_animation.setEndValue(1)
        self.fade_in_animation.start()
        
        if duration > 0:
            self.auto_hide_timer.start(duration)
    
    def hide_notification(self):
        """隐藏通知"""
        self.fade_out_animation.setStartValue(1)
        self.fade_out_animation.setEndValue(0)
        self.fade_out_animation.finished.connect(self.hide)
        self.fade_out_animation.start()


class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.title = title
        self._setup_card()
    
    def _setup_card(self):
        """设置卡片样式"""
        self.setFrameStyle(QFrame.Shape.NoFrame)
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(16, 16, 16, 16)
        self.main_layout.setSpacing(12)
        
        # 标题
        if self.title:
            title_label = QLabel(self.title)
            title_label.setFont(theme_manager.current_theme.get_font(
                size=theme_manager.current_theme.FONT_SIZE_TITLE,
                weight=QFont.Weight.DemiBold
            ))
            self.main_layout.addWidget(title_label)
        
        # 内容区域
        self.content_layout = QVBoxLayout()
        self.main_layout.addLayout(self.content_layout)
        
        # 设置样式
        self.setStyleSheet(f"""
            ModernCard {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border: 1px solid {theme_manager.current_theme.BORDER};
                border-radius: {theme_manager.current_theme.RADIUS_LARGE}px;
            }}
        """)
        
        # 添加阴影
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(16)
        shadow.setColor(QColor(0, 0, 0, 20))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
    
    def add_widget(self, widget: QWidget):
        """添加组件到卡片"""
        self.content_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局到卡片"""
        self.content_layout.addLayout(layout)


class ModernListWidget(QListWidget):
    """现代化列表组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_list()
    
    def _setup_list(self):
        """设置列表样式"""
        self.setAlternatingRowColors(False)
        self.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        
        # 自定义样式
        self.setStyleSheet(f"""
            ModernListWidget {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border: 1px solid {theme_manager.current_theme.BORDER};
                border-radius: {theme_manager.current_theme.RADIUS_MEDIUM}px;
                outline: none;
            }}
            
            ModernListWidget::item {{
                padding: {theme_manager.current_theme.SPACING_MD}px;
                border-bottom: 1px solid {theme_manager.current_theme.SEPARATOR};
                border-radius: {theme_manager.current_theme.RADIUS_SMALL}px;
                margin: 2px;
            }}
            
            ModernListWidget::item:selected {{
                background-color: {theme_manager.current_theme.ACCENT_BLUE};
                color: white;
            }}
            
            ModernListWidget::item:hover {{
                background-color: {theme_manager.current_theme.SURFACE_SECONDARY};
            }}
        """)


class ModernTextEdit(QTextEdit):
    """现代化文本编辑器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_text_edit()
    
    def _setup_text_edit(self):
        """设置文本编辑器样式"""
        self.setFont(theme_manager.current_theme.get_font())
        
        self.setStyleSheet(f"""
            ModernTextEdit {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border: 1px solid {theme_manager.current_theme.BORDER};
                border-radius: {theme_manager.current_theme.RADIUS_MEDIUM}px;
                padding: {theme_manager.current_theme.SPACING_MD}px;
                color: {theme_manager.current_theme.TEXT_PRIMARY};
            }}
            
            ModernTextEdit:focus {{
                border-color: {theme_manager.current_theme.ACCENT_BLUE};
            }}
        """)


class ModernLineEdit(QLineEdit):
    """现代化单行输入框"""
    
    def __init__(self, placeholder: str = "", parent=None):
        super().__init__(parent)
        if placeholder:
            self.setPlaceholderText(placeholder)
        self._setup_line_edit()
    
    def _setup_line_edit(self):
        """设置输入框样式"""
        self.setMinimumHeight(36)
        self.setFont(theme_manager.current_theme.get_font())
        
        self.setStyleSheet(f"""
            ModernLineEdit {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border: 1px solid {theme_manager.current_theme.BORDER};
                border-radius: {theme_manager.current_theme.RADIUS_MEDIUM}px;
                padding: 0 {theme_manager.current_theme.SPACING_MD}px;
                color: {theme_manager.current_theme.TEXT_PRIMARY};
            }}
            
            ModernLineEdit:focus {{
                border-color: {theme_manager.current_theme.ACCENT_BLUE};
            }}
            
            ModernLineEdit::placeholder {{
                color: {theme_manager.current_theme.TEXT_TERTIARY};
            }}
        """)


class ModernComboBox(QComboBox):
    """现代化下拉框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_combo_box()
    
    def _setup_combo_box(self):
        """设置下拉框样式"""
        self.setMinimumHeight(36)
        self.setFont(theme_manager.current_theme.get_font())
        
        self.setStyleSheet(f"""
            ModernComboBox {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border: 1px solid {theme_manager.current_theme.BORDER};
                border-radius: {theme_manager.current_theme.RADIUS_MEDIUM}px;
                padding: 0 {theme_manager.current_theme.SPACING_MD}px;
                color: {theme_manager.current_theme.TEXT_PRIMARY};
            }}
            
            ModernComboBox:hover {{
                border-color: {theme_manager.current_theme.ACCENT_BLUE};
            }}
            
            ModernComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            
            ModernComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {theme_manager.current_theme.TEXT_SECONDARY};
            }}
            
            ModernComboBox QAbstractItemView {{
                background-color: {theme_manager.current_theme.SURFACE_PRIMARY};
                border: 1px solid {theme_manager.current_theme.BORDER};
                border-radius: {theme_manager.current_theme.RADIUS_MEDIUM}px;
                selection-background-color: {theme_manager.current_theme.ACCENT_BLUE};
                outline: none;
            }}
        """)


class LoadingSpinner(QWidget):
    """加载动画组件"""
    
    def __init__(self, size: int = 32, parent=None):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        self._setup_spinner()
    
    def _setup_spinner(self):
        """设置加载动画"""
        self.setFixedSize(self.size, self.size)
        
        # 动画定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self._rotate)
        
    def start_animation(self):
        """开始动画"""
        self.timer.start(50)  # 20 FPS
        self.show()
    
    def stop_animation(self):
        """停止动画"""
        self.timer.stop()
        self.hide()
    
    def _rotate(self):
        """旋转动画"""
        self.angle = (self.angle + 10) % 360
        self.update()
    
    def paintEvent(self, event):
        """绘制加载动画"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置画笔
        pen = QPen(QColor(theme_manager.current_theme.ACCENT_BLUE))
        pen.setWidth(3)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        
        # 绘制圆弧
        rect = QRect(3, 3, self.size - 6, self.size - 6)
        painter.drawArc(rect, self.angle * 16, 120 * 16)  # 120度弧
