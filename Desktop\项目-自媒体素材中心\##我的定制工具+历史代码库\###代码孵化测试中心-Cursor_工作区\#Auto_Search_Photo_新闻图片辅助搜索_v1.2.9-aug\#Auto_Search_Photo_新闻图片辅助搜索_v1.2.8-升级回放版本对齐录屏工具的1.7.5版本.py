import os
import json
import shutil
import time
import subprocess
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from PIL import ImageGrab, Image, ImageTk
from pynput.mouse import <PERSON><PERSON>, Controller as MouseController
from pynput.keyboard import Key, Controller as KeyboardController
from pynput.keyboard import Listener
import threading
import queue
import traceback
import sys

class ModernDarkTheme:
    """现代深色主题样式配置"""
    # 基础颜色
    BACKGROUND = '#1a1a1a'  # 深黑色背景
    FOREGROUND = '#e0e0e0'  # 浅灰色文字
    
    # 特殊文本颜色
    PATH_FG = '#4a148c'     # 深紫色路径文本
    
    # 按钮颜色
    BUTTON_BG = '#2d2d2d'      # 深灰色按钮背景
    BUTTON_FG = '#e0e0e0'      # 浅灰色按钮文字
    BUTTON_ACTIVE_BG = '#3d3d3d'  # 按钮激活背景
    BUTTON_DISABLED_BG = '#252525'  # 禁用按钮背景
    BUTTON_DISABLED_FG = '#666666'  # 禁用按钮文字
    
    # 特殊按钮颜色
    BUTTON_RED = '#c62828'     # 深红色按钮
    BUTTON_GREEN = '#2e7d32'   # 深绿色按钮
    BUTTON_BLUE = '#1565c0'    # 深蓝色按钮
    BUTTON_YELLOW = '#f9a825'  # 金黄色按钮
    
    # 文本区域
    TEXT_BG = '#2d2d2d'        # 深灰色文本背景
    TEXT_FG = '#e0e0e0'        # 浅灰色文本
    
    # 输入框
    ENTRY_BG = '#2d2d2d'       # 深灰色输入框背景
    ENTRY_FG = '#4a148c'       # 深紫色输入框文字
    
    # 列表框
    LISTBOX_BG = '#2d2d2d'     # 深灰色列表框背景
    LISTBOX_FG = '#e0e0e0'     # 浅灰色列表框文字
    
    # 高亮颜色
    HIGHLIGHT_BG = '#4a148c'   # 深紫色高亮背景
    HIGHLIGHT_FG = '#ffffff'   # 白色高亮文字
    
    # 边框颜色
    BORDER_COLOR = '#333333'   # 深灰色边框
    
    # 滚动条颜色
    SCROLLBAR_BG = '#2d2d2d'   # 滚动条背景
    SCROLLBAR_FG = '#4a148c'   # 滚动条前景
    
    # 下拉框颜色
    COMBOBOX_BG = '#2d2d2d'    # 下拉框背景色
    COMBOBOX_FG = '#4a148c'    # 下拉框文字颜色

class CustomScrollbar(ttk.Scrollbar):
    """自定义滚动条"""
    def __init__(self, master=None, **kwargs):
        style = ttk.Style()
        style.configure(
            'Custom.Vertical.TScrollbar',
            background=ModernDarkTheme.SCROLLBAR_BG,
            troughcolor=ModernDarkTheme.SCROLLBAR_BG,
            arrowcolor=ModernDarkTheme.SCROLLBAR_FG
        )
        kwargs['style'] = 'Custom.Vertical.TScrollbar'
        super().__init__(master, **kwargs)

class CustomPathEntry(ttk.Entry):
    """自定义路径Entry控件"""
    def __init__(self, master=None, **kwargs):
        style = ttk.Style()
        style.configure(
            'Path.TEntry',
            fieldbackground=ModernDarkTheme.ENTRY_BG,
            foreground=ModernDarkTheme.PATH_FG,
            insertcolor=ModernDarkTheme.PATH_FG,
            borderwidth=0
        )
        kwargs['style'] = 'Path.TEntry'
        super().__init__(master, **kwargs)

class CustomButton(tk.Button):
    """自定义立体感按钮类"""
    def __init__(self, master=None, **kwargs):
        # 获取自定义背景色或使用默认色
        bg_color = kwargs.pop('bg', ModernDarkTheme.BUTTON_BG)
        self.default_bg = ModernDarkTheme.BUTTON_BG if bg_color == ModernDarkTheme.BUTTON_BG else bg_color
        
        # 计算不同状态的颜色
        self.hover_bg = self._adjust_color(self.default_bg, 1.2)  # 更亮
        self.active_bg = self._adjust_color(self.default_bg, 0.8)  # 更暗
        self.disabled_bg = self._adjust_color(ModernDarkTheme.BUTTON_DISABLED_BG, 0.9)
        
        # 设置按钮基本样式
        kwargs.update({
            'bg': self.default_bg,
            'fg': ModernDarkTheme.BUTTON_FG,
            'activebackground': self.active_bg,
            'activeforeground': ModernDarkTheme.BUTTON_FG,
            'disabledforeground': ModernDarkTheme.BUTTON_DISABLED_FG,
            'relief': 'raised',  # 使用凸起效果
            'borderwidth': 1,    # 边框宽度
            'padx': 15,
            'pady': 8,
            'font': ('Arial', 9, 'bold'),
            'cursor': 'hand2'    # 手型光标
        })
        
        super().__init__(master, **kwargs)
        
        # 创建立体效果
        self._create_3d_effect()
        
        # 绑定事件
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
        self.bind('<Button-1>', self.on_press)
        self.bind('<ButtonRelease-1>', self.on_release)

    def _create_3d_effect(self):
        """创建立体效果框架"""
        # 顶部亮色边框
        self.top_frame = tk.Frame(
            self,
            bg=self._adjust_color(self.default_bg, 1.3),
            height=1
        )
        self.top_frame.place(relx=0, rely=0, relwidth=1, height=1)
        
        # 左侧亮色边框
        self.left_frame = tk.Frame(
            self,
            bg=self._adjust_color(self.default_bg, 1.2),
            width=1
        )
        self.left_frame.place(relx=0, rely=0, width=1, relheight=1)
        
        # 底部暗色边框
        self.bottom_frame = tk.Frame(
            self,
            bg=self._adjust_color(self.default_bg, 0.7),
            height=1
        )
        self.bottom_frame.place(relx=0, rely=1, relwidth=1, height=1)
        
        # 右侧暗色边框
        self.right_frame = tk.Frame(
            self,
            bg=self._adjust_color(self.default_bg, 0.8),
            width=1
        )
        self.right_frame.place(relx=1, rely=0, width=1, relheight=1)

    def _adjust_color(self, color, factor):
        """调整颜色亮度"""
        try:
            # 将颜色转换为RGB
            if color.startswith('#'):
                rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
            else:
                return color
            
            # 调整亮度
            new_rgb = tuple(min(255, int(c * factor)) for c in rgb)
            
            # 转回十六进制
            return f'#{new_rgb[0]:02x}{new_rgb[1]:02x}{new_rgb[2]:02x}'
        except:
            return color

    def _update_3d_effect(self, pressed=False):
        """更新3D效果"""
        if pressed:
            # 按下状态 - 反转边框颜色
            self.top_frame.configure(bg=self._adjust_color(self.default_bg, 0.7))
            self.left_frame.configure(bg=self._adjust_color(self.default_bg, 0.8))
            self.bottom_frame.configure(bg=self._adjust_color(self.default_bg, 1.3))
            self.right_frame.configure(bg=self._adjust_color(self.default_bg, 1.2))
        else:
            # 正常状态
            self.top_frame.configure(bg=self._adjust_color(self.default_bg, 1.3))
            self.left_frame.configure(bg=self._adjust_color(self.default_bg, 1.2))
            self.bottom_frame.configure(bg=self._adjust_color(self.default_bg, 0.7))
            self.right_frame.configure(bg=self._adjust_color(self.default_bg, 0.8))

    def on_enter(self, e):
        """鼠标进入时的效果"""
        if self['state'] != 'disabled':
            self.configure(background=self.hover_bg)
            # 更新边框亮度
            self._update_3d_effect()

    def on_leave(self, e):
        """鼠标离开时的效果"""
        if self['state'] != 'disabled':
            self.configure(background=self.default_bg)
            self._update_3d_effect()

    def on_press(self, e):
        """鼠标按下时的效果"""
        if self['state'] != 'disabled':
            self.configure(background=self.active_bg)
            self._update_3d_effect(pressed=True)

    def on_release(self, e):
        """鼠标释放时的效果"""
        if self['state'] != 'disabled':
            self.configure(background=self.hover_bg)
            self._update_3d_effect()

    def configure(self, **kwargs):
        """重写configure方法以处理状态变化"""
        if 'state' in kwargs:
            if kwargs['state'] == 'disabled':
                super().configure(background=self.disabled_bg)
                # 禁用状态下的边框效果
                self.top_frame.configure(bg=self._adjust_color(self.disabled_bg, 1.1))
                self.left_frame.configure(bg=self._adjust_color(self.disabled_bg, 1.1))
                self.bottom_frame.configure(bg=self._adjust_color(self.disabled_bg, 0.9))
                self.right_frame.configure(bg=self._adjust_color(self.disabled_bg, 0.9))
            else:
                super().configure(background=self.default_bg)
                self._update_3d_effect()
        super().configure(**kwargs)

class CustomEntry(ttk.Entry):
    """自定义Entry控件"""
    def __init__(self, master=None, **kwargs):
        style = ttk.Style()
        style.configure(
            'Custom.TEntry',
            fieldbackground=ModernDarkTheme.ENTRY_BG,
            foreground=ModernDarkTheme.ENTRY_FG,
            insertcolor=ModernDarkTheme.ENTRY_FG,
            borderwidth=0
        )
        kwargs['style'] = 'Custom.TEntry'
        super().__init__(master, **kwargs)

class Logger:
    """日志记录器"""
    def __init__(self, text_widget):
        self.terminal = sys.stdout
        self.text_widget = text_widget

    def write(self, message):
        self.terminal.write(message)
        self.text_widget.insert(tk.END, message)
        self.text_widget.see(tk.END)
        self.text_widget.update()

    def flush(self):
        pass

class ModernFrame(ttk.Frame):
    """现代风格Frame"""
    def __init__(self, master=None, **kwargs):
        style = ttk.Style()
        style.configure(
            'Modern.TFrame',
            background=ModernDarkTheme.BACKGROUND,
            borderwidth=1,
            relief='solid'
        )
        kwargs['style'] = 'Modern.TFrame'
        super().__init__(master, **kwargs)

class ImageSearchAutomation:
    def execute_record(self, actions: List[dict]):
        """执行录制的动作序列 - 升级版本支持1.7.5规格"""
        try:
            # 获取播放速度
            speed = float(self.speed_var.get())
            
            # 重置开始时间和状态
            self.start_time = time.time()
            self.last_action_time = self.start_time
            
            # 状态管理
            keyboard_state = set()  # 当前按下的键的集合
            modifier_state = set()  # 当前按下的修饰键
            mouse_state = {
                'left': False,
                'right': False,
                'is_dragging': False,
                'drag_start': None,
                'drag_button': None
            }
            
            for action in actions:
                if not self.is_playing:
                    break
                    
                action_type = action['type']
                data = action['data']
                
                # 计算间隔时间
                interval = data.get('interval', 0.1) / speed
                time.sleep(interval)
                
                try:
                    # 处理组合键状态
                    if 'combination' in data:
                        current_keys = set(data['combination'])
                        # 释放不再需要的键
                        for key in keyboard_state - current_keys:
                            try:
                                if key.startswith('Key.'):
                                    self.keyboard.release(eval(key))
                                else:
                                    self.keyboard.release(key)
                            except:
                                pass
                        keyboard_state = current_keys
                    
                    # 处理修饰键
                    if 'modifiers' in data:
                        current_modifiers = set(data['modifiers'])
                        # 释放不再需要的修饰键
                        for mod in modifier_state - current_modifiers:
                            try:
                                self.keyboard.release(eval(mod))
                            except:
                                pass
                        # 按下新的修饰键
                        for mod in current_modifiers - modifier_state:
                            try:
                                self.keyboard.press(eval(mod))
                            except:
                                pass
                        modifier_state = current_modifiers
                        
                    # 执行动作
                    if action_type == 'mouse_move':
                        self.mouse.position = (data['x'], data['y'])
                        print(f"鼠标移动到: ({data['x']}, {data['y']})")
                        
                    elif action_type == 'mouse_click':
                        self.mouse.position = (data['x'], data['y'])
                        button = Button.left if data['button'] == 'left' else Button.right
                        self.mouse.click(button)
                        print(f"鼠标点击: {data['button']}")
                        
                    elif action_type == 'mouse_press':
                        self.mouse.position = (data['x'], data['y'])
                        button = Button.left if data['button'] == 'left' else Button.right
                        self.mouse.press(button)
                        mouse_state.update({
                            data['button']: True,
                            'is_dragging': True,
                            'drag_start': (data['x'], data['y']),
                            'drag_button': data['button']
                        })
                        print(f"鼠标按下: {data['button']}")
                        
                    elif action_type == 'mouse_drag':
                        self.mouse.position = (data['x'], data['y'])
                        if mouse_state['is_dragging']:
                            print(f"鼠标拖动到: ({data['x']}, {data['y']})")
                            
                    elif action_type == 'mouse_release':
                        self.mouse.position = (data['x'], data['y'])
                        button = Button.left if data['button'] == 'left' else Button.right
                        self.mouse.release(button)
                        mouse_state.update({
                            data['button']: False,
                            'is_dragging': False,
                            'drag_start': None,
                            'drag_button': None
                        })
                        print(f"鼠标释放: {data['button']}")
                        
                    elif action_type == 'key_press':
                        key = data['key']
                        # 处理普通键
                        if key.startswith('Key.'):
                            self.keyboard.press(eval(key))
                        elif key.startswith("'") and key.endswith("'"):
                            self.keyboard.press(eval(key))
                        else:
                            self.keyboard.press(key)
                        keyboard_state.add(key)
                        print(f"按键按下: {key}")
                        
                    elif action_type == 'key_release':
                        key = data['key']
                        if key in keyboard_state:
                            if key.startswith('Key.'):
                                self.keyboard.release(eval(key))
                            elif key.startswith("'") and key.endswith("'"):
                                self.keyboard.release(eval(key))
                            else:
                                self.keyboard.release(key)
                            keyboard_state.remove(key)
                            print(f"按键释放: {key}")
                            
                except Exception as e:
                    print(f"执行动作失败: {str(e)}")
                    continue
                    
                # 更新最后动作时间
                self.last_action_time = time.time()
                
        except Exception as e:
            print(f"执行动作失败: {str(e)}")
            traceback.print_exc()
            
        finally:
            # 确保释放所有按键和鼠标按键
            self.release_all_states(keyboard_state, modifier_state, mouse_state)
            
            self.start_time = None
            self.last_action_time = None
            
            # 根据当前操作更新UI状态
            self.update_ui_after_action()
            
    def release_all_states(self, keyboard_state, modifier_state, mouse_state):
        """释放所有按键和鼠标状态"""
        try:
            # 释放鼠标按键
            for button_name in ['left', 'right']:
                if mouse_state.get(button_name):
                    try:
                        button = Button.left if button_name == 'left' else Button.right
                        self.mouse.release(button)
                    except:
                        pass
            
            # 释放普通键
            for key in keyboard_state:
                try:
                    if key.startswith('Key.'):
                        self.keyboard.release(eval(key))
                    elif key.startswith("'") and key.endswith("'"):
                        self.keyboard.release(eval(key))
                    else:
                        self.keyboard.release(key)
                except:
                    pass
            
            # 释放修饰键
            for mod in modifier_state:
                try:
                    self.keyboard.release(eval(mod))
                except:
                    pass
                    
        except Exception as e:
            print(f"释放按键状态失败: {str(e)}")
            traceback.print_exc()

    def load_folders(self):
        """加载并扫描文件夹"""
        try:
            root_path = self.path_entry.get()
            if not root_path or not os.path.exists(root_path):
                error_msg = "请选择有效的文件夹路径"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            print(f"开始扫描文件夹: {root_path}")
            
            # 扫描文件夹
            self.scanned_folders = self.scan_folders(root_path)
            if not self.scanned_folders:
                warn_msg = "未找到符合条件的文件夹"
                print(warn_msg)
                messagebox.showwarning("警告", warn_msg)
                return
                
            # 更新文件夹列表显示
            self.ui_queue.put({
                'type': 'folder_list',
                'folders': self.scanned_folders
            })

            # 设置第一个文件夹为当前文件夹
            self.current_folder = self.scanned_folders[0]
            
            status_msg = f'已找到 {len(self.scanned_folders)} 个文件夹，可以开始AI分析'
            print(status_msg)
            self.ui_queue.put({
                'type': 'status',
                'text': status_msg
            })
            
            # 更新按钮状态
            button_states = {
                'start_search': False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': False,
                'next_folder': False,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
        except Exception as e:
            error_msg = f"加载文件夹失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            
    def __init__(self):
        # 基础初始化
        self.root = tk.Tk()
        self.root.title("AI图片搜索助手 Pro")
        self.root.geometry("1280x1100")
        self.root.configure(bg=ModernDarkTheme.BACKGROUND)

        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(__file__), 'icon.ico')
        if os.path.exists(icon_path):
            self.root.iconbitmap(icon_path)

        # 初始化变量
        self.init_variables()
        
        # 设置UI组件
        self.setup_ui()
        
        # 设置UI更新队列
        self.ui_queue = queue.Queue()
        self.update_ui()
        
        # 设置键盘监听器
        self.setup_keyboard_listener()
        
        # 加载默认路径
        self.default_path = os.path.dirname(os.path.abspath(__file__))
        
        # 加载配置文件
        self.config_path = os.path.join(self.default_path, 'config.json')
        self.load_config()
        
        # 检查本地模型
        self.check_local_models()
        
        # 初始化控制器
        try:
            self.mouse = MouseController()
            self.keyboard = KeyboardController()
        except Exception as e:
            messagebox.showerror("错误", f"控制器初始化失败: {str(e)}")
            return
        
        # 新增：下载监控相关的属性
        self.download_start_time = None
        self.monitored_files = set()  # 记录监控期间的新文件
        self.is_monitoring = False
        self.monitor_thread = None
        self.monitoring_lock = threading.Lock()     

        # 新增：历史记录和状态管理
        self.history_stack = []  # 操作历史栈
        self.max_history = 10    # 最大历史记录数
        
        # 新增：监控增强
        self.monitor_remaining_time = 0  # 剩余监控时间
        self.monitor_update_interval = 1000  # 监控更新间隔(ms)
        self.early_stop_monitoring = False   # 提前停止监控标志
        
        # 新增：重试相关
        self.max_move_retries = 3  # 最大重试次数
        self.retry_delay = 1.0     # 重试延迟(秒)
        
           
            
        print("程序初始化完成")

    def init_variables(self):
        """初始化变量"""
        self.is_playing = False
        self.current_folder = ""
        self.current_keywords = []
        self.folder_keyword_map = {}
        self.recorded_actions_search = None
        self.recorded_actions_download = None
        self.scanned_folders = []
        self.last_state = None
        self.keyboard_listener = None
        self.start_time = None
        self.last_action_time = None
        self.current_state = "ready"
        
        # 新增：下载目录配置
        self.download_dir = os.path.expanduser("~/Downloads")
        
        # 新增：可用模型列表
        self.available_models = []

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.download_dir = config.get('download_dir', self.download_dir)
            else:
                # 创建默认配置
                self.save_config()
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            traceback.print_exc()

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'download_dir': self.download_dir
            }
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
            traceback.print_exc()

    def check_local_models(self):
        """检查本地已安装的模型"""
        try:
            result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            if result.returncode == 0:
                # 解析 ollama list 的输出
                installed_models = []
                for line in result.stdout.split('\n')[1:]:  # 跳过header行
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 2:
                            model_name = parts[0].lower()
                            # 处理带latest的情况
                            if ':latest' in model_name:
                                model_name = model_name.replace(':latest', '')
                                
                            # 根据实际输出映射到我们的模型ID
                            if 'qwen2.5:32b' in model_name:
                                installed_models.append('qwen2.5:32b')
                            elif 'qwen2.5:14b' in model_name:
                                installed_models.append('qwen2.5:14b')
                            elif 'qwen2.5:7b' in model_name or 'qwen2.5:latest' in model_name:
                                installed_models.append('qwen2.5:7b')
                            elif 'qwen2:7b' in model_name:
                                installed_models.append('qwen2:7b')
                            elif 'llama3.1' in model_name:
                                installed_models.append('llama3.1')
                
                self.available_models = installed_models
                print("可用的本地模型:", self.available_models)
                
                # 更新UI中的radio buttons状态
                for value, radio in self.model_radios.items():
                    if value in self.available_models:
                        radio.configure(state='normal')
                    else:
                        radio.configure(state='disabled')
                
                # 如果当前选择的模型不可用，自动选择第一个可用的模型
                if self.ai_model_var.get() not in self.available_models and self.available_models:
                    self.ai_model_var.set(self.available_models[0])
                    
            else:
                print("无法获取本地模型列表")
                print("命令输出:", result.stdout)
                print("错误信息:", result.stderr)
                self.available_models = []
                
        except Exception as e:
            print(f"检查本地模型失败: {str(e)}")
            traceback.print_exc()
            self.available_models = []

    def setup_ui(self):
        """设置UI界面"""
        # 主容器
        main_frame = ModernFrame(self.root)
        main_frame.pack(fill='both', expand=True, padx=15, pady=10)
        
        # 设置样式
        self.setup_styles()
        
        # 顶部区域
        top_frame = self.create_top_frame(main_frame)
        top_frame.pack(fill='x', pady=(0, 10))
        
        # 主体区域
        body_frame = self.create_body_frame(main_frame)
        body_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # 底部控制区域
        control_frame = self.create_control_frame(main_frame)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # 状态栏
        status_frame = self.create_status_frame(main_frame)
        status_frame.pack(fill='x')
        
        print("界面初始化完成")

    def setup_styles(self):
        """设置控件样式"""
        style = ttk.Style()
        
        # 通用样式
        style.configure('TFrame', background=ModernDarkTheme.BACKGROUND)
        style.configure('TLabel', 
                       background=ModernDarkTheme.BACKGROUND,
                       foreground=ModernDarkTheme.FOREGROUND,
                       font=('Arial', 9))
        style.configure('TLabelframe',
                       background=ModernDarkTheme.BACKGROUND,
                       foreground=ModernDarkTheme.FOREGROUND)
        style.configure('TLabelframe.Label',
                       background=ModernDarkTheme.BACKGROUND,
                       foreground=ModernDarkTheme.FOREGROUND,
                       font=('Arial', 9, 'bold'))
                       
        # Radio按钮样式
        style.configure('TRadiobutton',
                       background=ModernDarkTheme.BACKGROUND,
                       foreground=ModernDarkTheme.FOREGROUND,
                       font=('Arial', 9))
                       
        # Combobox样式
        style.configure('TCombobox',
                       background=ModernDarkTheme.ENTRY_BG,
                       foreground=ModernDarkTheme.ENTRY_FG,
                       fieldbackground=ModernDarkTheme.ENTRY_BG,
                       selectbackground=ModernDarkTheme.HIGHLIGHT_BG)

    def setup_combobox_style(self):
        """设置下拉框样式"""
        style = ttk.Style()
        style.configure(
            'TCombobox',
            fieldbackground=ModernDarkTheme.COMBOBOX_BG,
            foreground=ModernDarkTheme.COMBOBOX_FG,
            selectbackground=ModernDarkTheme.HIGHLIGHT_BG,
            selectforeground=ModernDarkTheme.HIGHLIGHT_FG
        )
        
        # 修改下拉列表样式
        self.root.option_add('*TCombobox*Listbox.background', ModernDarkTheme.COMBOBOX_BG)
        self.root.option_add('*TCombobox*Listbox.foreground', ModernDarkTheme.COMBOBOX_FG)
        self.root.option_add('*TCombobox*Listbox.selectBackground', ModernDarkTheme.HIGHLIGHT_BG)
        self.root.option_add('*TCombobox*Listbox.selectForeground', ModernDarkTheme.HIGHLIGHT_FG)

    def create_top_frame(self, parent):
        """创建顶部区域"""
        top_frame = ModernFrame(parent)
        
        # 路径选择区域
        path_frame = ModernFrame(top_frame)
        path_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Label(path_frame, text="文件夹路径：").pack(side=tk.LEFT, padx=(5, 0))
        self.path_entry = CustomPathEntry(path_frame)
        self.path_entry.pack(side=tk.LEFT, fill='x', expand=True, padx=5)
        
        CustomButton(path_frame, text="浏览", 
                    command=self.browse_folder).pack(side=tk.LEFT)
        CustomButton(path_frame, text="加载文件夹",
                    command=self.load_folders,
                    bg=ModernDarkTheme.BUTTON_BLUE).pack(side=tk.LEFT, padx=5)
        CustomButton(path_frame, text="加载分析结果",
                    command=self.load_analysis_results,
                    bg=ModernDarkTheme.BUTTON_BLUE).pack(side=tk.LEFT, padx=5)
        CustomButton(path_frame, text="开始AI分析",
                    command=self.start_analysis,
                    bg=ModernDarkTheme.BUTTON_GREEN).pack(side=tk.LEFT)
        
        # 下载目录选择区域
        download_frame = ModernFrame(top_frame)
        download_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Label(download_frame, text="下载目录：").pack(side=tk.LEFT, padx=(5, 0))
        self.download_entry = CustomPathEntry(download_frame)
        self.download_entry.pack(side=tk.LEFT, fill='x', expand=True, padx=5)
        self.download_entry.insert(0, self.download_dir)
        
        CustomButton(download_frame, text="浏览",
                    command=self.browse_download_dir).pack(side=tk.LEFT)
        CustomButton(download_frame, text="保存设置",
                    command=self.save_download_dir,
                    bg=ModernDarkTheme.BUTTON_BLUE).pack(side=tk.LEFT, padx=5)
        
        # AI模型选择
        model_frame = ModernFrame(top_frame)
        model_frame.pack(fill='x')
        
        models_label = ttk.Label(model_frame, text="AI模型：")
        models_label.pack(side=tk.LEFT, padx=(5, 10))
        
        self.ai_model_var = tk.StringVar(value="qwen2.5:32b")  # 默认值
        models = [
            ("Qwen2.5 32B", "qwen2.5:32b"),
            ("Qwen2.5 14B", "qwen2.5:14b"),
            ("Qwen2.5 7B", "qwen2.5:7b"),
            ("Qwen2 7B", "qwen2:7b"),
            ("Llama3.1", "llama3.1")
        ]
        
        self.model_radios = {}  # 保存radio button的引用
        for text, value in models:
            radio = ttk.Radiobutton(
                model_frame, 
                text=text,
                value=value,
                variable=self.ai_model_var)
            radio.pack(side=tk.LEFT, padx=5)
            self.model_radios[value] = radio
            
            # 如果模型不可用，禁用对应的radio button
            if value not in self.available_models:
                radio.configure(state='disabled')
        
        # 如果没有可用模型，选择第一个模型并禁用
        if not self.available_models:
            self.ai_model_var.set(models[0][1])
            
        # 播放速度选择
        speed_frame = ModernFrame(model_frame)
        speed_frame.pack(side=tk.RIGHT, padx=10)
        
        ttk.Label(speed_frame, text="播放速度：").pack(side=tk.LEFT)
        
        self.speed_var = tk.StringVar(value="1.0")
        speeds = ["1.0", "1.5", "2.0", "3.0", "4.0"]
        self.speed_combo = ttk.Combobox(
            speed_frame,
            values=speeds,
            textvariable=self.speed_var,
            state="readonly",
            width=5
        )
        self.speed_combo.pack(side=tk.LEFT, padx=5)
        
        return top_frame

    def create_body_frame(self, parent):
        """创建主体区域"""
        body_frame = ModernFrame(parent)
        
        # 录制文件加载区域
        record_frame = ttk.LabelFrame(body_frame, text="录制文件")
        record_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(record_frame, text="搜索录制：").pack(side=tk.LEFT, padx=(5, 0))
        self.search_record_entry = CustomEntry(record_frame)
        self.search_record_entry.pack(side=tk.LEFT, fill='x', expand=True, padx=5)
        
        CustomButton(record_frame, text="加载",
                    command=lambda: self.load_record_file('search')).pack(side=tk.LEFT)
        
        ttk.Label(record_frame, text="下载录制：").pack(side=tk.LEFT, padx=(10,0))
        self.download_record_entry = CustomEntry(record_frame)
        self.download_record_entry.pack(side=tk.LEFT, fill='x', expand=True, padx=5)
        
        CustomButton(record_frame, text="加载",
                    command=lambda: self.load_record_file('download')).pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(body_frame, text="运行日志")
        log_frame.pack(fill='x', expand=True, pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(
            log_frame, 
            height=8,
            bg=ModernDarkTheme.TEXT_BG,
            fg=ModernDarkTheme.TEXT_FG,
            insertbackground=ModernDarkTheme.TEXT_FG,
            font=('Consolas', 9)
        )
        self.log_text.pack(fill='both', expand=True, padx=2, pady=2)
        
        sys.stdout = Logger(self.log_text)
        sys.stderr = Logger(self.log_text)

        # 信息显示区域
        info_frame = ttk.LabelFrame(body_frame, text="处理信息")
        info_frame.pack(fill='both', expand=True)
        
        # 左右分栏
        left_frame = ModernFrame(info_frame)
        left_frame.pack(side=tk.LEFT, fill='both', expand=True, padx=5, pady=5)
        
        right_frame = ModernFrame(info_frame)
        right_frame.pack(side=tk.LEFT, fill='both', expand=True, padx=5, pady=5)
        
        # 左侧：文件夹列表
        folder_label = ttk.Label(left_frame, text="待处理文件夹：")
        folder_label.pack(fill='x')
        
        folder_container = ModernFrame(left_frame)
        folder_container.pack(fill='both', expand=True)
        
        self.folder_listbox = tk.Listbox(
            folder_container,
            bg=ModernDarkTheme.LISTBOX_BG,
            fg=ModernDarkTheme.LISTBOX_FG,
            selectmode=tk.SINGLE,
            font=('Arial', 9),
            relief='flat',
            borderwidth=0,
            highlightthickness=1,
            highlightbackground=ModernDarkTheme.BORDER_COLOR,
            selectbackground=ModernDarkTheme.HIGHLIGHT_BG,
            selectforeground=ModernDarkTheme.HIGHLIGHT_FG
        )
        self.folder_listbox.pack(side=tk.LEFT, fill='both', expand=True)
        
        folder_scrollbar = CustomScrollbar(folder_container, orient="vertical", 
                                         command=self.folder_listbox.yview)
        folder_scrollbar.pack(side=tk.RIGHT, fill='y')
        self.folder_listbox.configure(yscrollcommand=folder_scrollbar.set)
        
        # 右侧：当前处理信息
        ttk.Label(right_frame, text="当前处理：").pack(fill='x')
        self.current_info = tk.Text(
            right_frame,
            height=4,
            bg=ModernDarkTheme.TEXT_BG,
            fg=ModernDarkTheme.TEXT_FG,
            font=('Arial', 9),
            relief='flat',
            borderwidth=0,
            highlightthickness=1,
            highlightbackground=ModernDarkTheme.BORDER_COLOR
        )
        self.current_info.pack(fill='x', pady=(0, 5))
        
        ttk.Label(right_frame, text="当前搜索关键词：").pack(fill='x')
        self.current_keyword_text = tk.Text(
            right_frame,
            height=2,
            bg=ModernDarkTheme.TEXT_BG,
            fg=ModernDarkTheme.TEXT_FG,
            font=('Arial', 9),
            relief='flat',
            borderwidth=0,
            highlightthickness=1,
            highlightbackground=ModernDarkTheme.BORDER_COLOR
        )
        self.current_keyword_text.pack(fill='x', pady=(0, 5))
        
        ttk.Label(right_frame, text="生成的关键词：").pack(fill='x')
        keyword_container = ModernFrame(right_frame)
        keyword_container.pack(fill='both', expand=True)
        
        self.keyword_text = tk.Text(
            keyword_container,
            bg=ModernDarkTheme.TEXT_BG,
            fg=ModernDarkTheme.TEXT_FG,
            font=('Arial', 9),
            relief='flat',
            borderwidth=0,
            highlightthickness=1,
            highlightbackground=ModernDarkTheme.BORDER_COLOR
        )
        self.keyword_text.pack(side=tk.LEFT, fill='both', expand=True)
        
        keyword_scrollbar = CustomScrollbar(keyword_container, orient="vertical", 
                                          command=self.keyword_text.yview)
        keyword_scrollbar.pack(side=tk.RIGHT, fill='y')
        self.keyword_text.configure(yscrollcommand=keyword_scrollbar.set)
        
        return body_frame
        
    def create_control_frame(self, parent):
        """创建控制按钮区域 - 添加后退按钮"""
        control_frame = ttk.LabelFrame(parent, text="控制面板")
        
        # 增加内边距来避免和边框重叠
        inner_frame = ModernFrame(control_frame)
        inner_frame.pack(fill='x', padx=5, pady=(0, 5))  # 添加水平内边距，底部留出空间
        
        # 第一行按钮
        button_frame1 = ModernFrame(inner_frame)
        button_frame1.pack(fill='x', pady=2)
        
        # 调整按钮之间的间距
        self.start_search_btn = CustomButton(
            button_frame1, 
            text="开始搜索",
            command=self.start_search,
            bg=ModernDarkTheme.BUTTON_GREEN
        )
        self.start_search_btn.pack(side=tk.LEFT, padx=(0, 4))  # 减小按钮间距
        
        self.execute_download_btn = CustomButton(
            button_frame1,
            text="执行下载",
            command=self.execute_download,
            bg=ModernDarkTheme.BUTTON_BLUE,
            state='disabled'
        )
        self.execute_download_btn.pack(side=tk.LEFT, padx=4)
        
        self.move_files_btn = CustomButton(
            button_frame1,
            text="移动文件",
            command=self.move_downloaded_files,
            bg=ModernDarkTheme.BUTTON_YELLOW,
            state='disabled'
        )
        self.move_files_btn.pack(side=tk.LEFT, padx=4)
        
        self.stop_btn = CustomButton(
            button_frame1,
            text="停止",
            command=self.stop_search,
            bg=ModernDarkTheme.BUTTON_RED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=4)
        
        # 第二行按钮
        button_frame2 = ModernFrame(inner_frame)
        button_frame2.pack(fill='x', pady=2)
        
        self.next_keyword_btn = CustomButton(
            button_frame2,
            text="下一关键词",
            command=self.next_keyword,
            state='disabled'
        )
        self.next_keyword_btn.pack(side=tk.LEFT, padx=(0, 4))
        
        self.next_folder_btn = CustomButton(
            button_frame2,
            text="下一文件夹",
            command=self.next_folder,
            state='disabled'
        )
        self.next_folder_btn.pack(side=tk.LEFT, padx=4)

        # 添加后退按钮
        self.back_btn = CustomButton(
            button_frame1,
            text="后退",
            command=self.go_back,
            bg=ModernDarkTheme.BUTTON_RED,
            state='disabled'
        )
        self.back_btn.pack(side=tk.LEFT, padx=4)
        
        # 添加停止监控按钮
        self.stop_monitor_btn = CustomButton(
            button_frame1,
            text="停止监控",
            command=self.stop_monitoring_early,
            bg=ModernDarkTheme.BUTTON_YELLOW,
            state='disabled'
        )
        self.stop_monitor_btn.pack(side=tk.LEFT, padx=4)
          
        return control_frame
    
    def push_history(self):
        """保存当前状态到历史栈"""
        current_state = {
            'current_folder': self.current_folder,
            'current_keywords': self.current_keywords.copy() if self.current_keywords else [],
            'monitored_files': self.monitored_files.copy(),
            'current_state': self.current_state,
            'is_playing': self.is_playing
        }
        
        self.history_stack.append(current_state)
        if len(self.history_stack) > self.max_history:
            self.history_stack.pop(0)
            
        # 更新后退按钮状态
        self.back_btn.config(state='normal' if self.history_stack else 'disabled')    

    def go_back(self):
        """执行后退操作"""
        try:
            if not self.history_stack:
                return
                
            # 停止当前活动
            self.is_playing = False
            self.stop_download_monitoring()
            
            # 恢复上一个状态
            previous_state = self.history_stack.pop()
            self.current_folder = previous_state['current_folder']
            self.current_keywords = previous_state['current_keywords'].copy()
            self.monitored_files = previous_state['monitored_files'].copy()
            self.current_state = previous_state['current_state']
            self.is_playing = False
            
            # 更新UI
            self.update_current_info()
            self.scroll_to_current_keyword()
            
            # 更新剪贴板
            if self.current_keywords:
                self.root.clipboard_clear()
                self.root.clipboard_append(self.current_keywords[0])
            
            # 更新按钮状态
            self.update_button_states_for_state(self.current_state)
            
            print("已回退到上一步状态")
            self.ui_queue.put({
                'type': 'status',
                'text': '已回退到上一步状态'
            })
            
        except Exception as e:
            print(f"后退操作失败: {str(e)}")
            traceback.print_exc()     

    def update_button_states_for_state(self, state):
        """根据状态更新按钮状态"""
        button_states = {
            'ready': {
                'start_search': True,
                'execute_download': False,
                'move_files': False,
                'next_keyword': bool(self.current_keywords),
                'next_folder': True,
                'stop': True,
                'back': bool(self.history_stack),
                'stop_monitor': False
            },
            'searching': {
                'start_search': False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True,
                'next_folder': True,
                'stop': True,
                'back': False,
                'stop_monitor': False
            },
            'search_complete': {
                'start_search': False,
                'execute_download': True,
                'move_files': True,
                'next_keyword': True,
                'next_folder': True,
                'stop': True,
                'back': True,
                'stop_monitor': False
            },
            'downloading': {
                'start_search': False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True,
                'next_folder': True,
                'stop': True,
                'back': False,
                'stop_monitor': True
            },
            'download_complete': {
                'start_search': False,
                'execute_download': False,
                'move_files': True,
                'next_keyword': True,
                'next_folder': True,
                'stop': True,
                'back': True,
                'stop_monitor': False
            }
        }
        
        if state in button_states:
            states = button_states[state]
            self.ui_queue.put({
                'type': 'button_states',
                'states': states
            })               

    def create_status_frame(self, parent):
        """创建状态栏"""
        status_frame = ModernFrame(parent)
        
        self.status_label = ttk.Label(
            status_frame, 
            text="就绪",
            background=ModernDarkTheme.BACKGROUND,
            foreground=ModernDarkTheme.FOREGROUND,
            font=('Arial', 9)
        )
        self.status_label.pack(side=tk.LEFT, fill='x', padx=5)
        
        self.progress_label = ttk.Label(
            status_frame,
            text="",
            background=ModernDarkTheme.BACKGROUND,
            foreground=ModernDarkTheme.FOREGROUND,
            font=('Arial', 9)
        )
        self.progress_label.pack(side=tk.RIGHT, padx=5)
        
        return status_frame

    def browse_download_dir(self):
        """浏览选择下载目录"""
        download_dir = filedialog.askdirectory(initialdir=self.download_dir)
        if download_dir:
            self.download_entry.delete(0, tk.END)
            self.download_entry.insert(0, download_dir)
            print(f"已选择下载目录: {download_dir}")

    def save_download_dir(self):
        """保存下载目录设置"""
        new_dir = self.download_entry.get()
        if os.path.exists(new_dir):
            self.download_dir = new_dir
            self.save_config()
            print(f"已保存下载目录设置: {new_dir}")
        else:
            messagebox.showerror("错误", "请选择有效的下载目录")

    def load_analysis_results(self):
        """加载已保存的分析结果"""
        try:
            # 检查是否已加载新路径
            current_path = self.path_entry.get()
            if not current_path or not os.path.exists(current_path):
                error_msg = "请先加载新的文件夹路径，再加载分析结果"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            filename = filedialog.askopenfilename(
                initialdir=self.default_path,
                title="选择分析结果文件",
                filetypes=[("JSON Files", "*.json")]
            )
            
            if not filename:
                return
                
            print("\n=== 加载分析结果 ===")
            print(f"当前工作路径: {current_path}")
            print(f"加载结果文件: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if not isinstance(data, dict) or 'analysis_date' not in data or 'keywords' not in data:
                raise ValueError("无效的分析结果文件格式")
                
            # 路径转换
            converted_map = {}
            conversion_report = {
                'success': 0,
                'failed': 0,
                'original_paths': [],
                'new_paths': []
            }
            
            print("\n开始路径转换...")
            for old_path, keywords in data['keywords'].items():
                # 获取原始文件夹名称
                base_name = os.path.basename(old_path)
                found = False
                
                # 在新的根路径下查找匹配的文件夹
                for root, dirs, _ in os.walk(current_path):
                    for dir_name in dirs:
                        if dir_name == base_name:
                            new_path = os.path.join(root, dir_name)
                            converted_map[new_path] = keywords
                            conversion_report['success'] += 1
                            conversion_report['original_paths'].append(old_path)
                            conversion_report['new_paths'].append(new_path)
                            print(f"\n找到匹配文件夹:")
                            print(f"原路径: {old_path}")
                            print(f"新路径: {new_path}")
                            found = True
                            break
                    if found:
                        break
                        
                if not found:
                    conversion_report['failed'] += 1
                    print(f"\n警告: 未找到匹配文件夹:")
                    print(f"原文件夹: {base_name}")
                    print(f"在路径下查找失败: {current_path}")
            
            # 打印转换报告
            print("\n=== 路径转换报告 ===")
            print(f"成功转换: {conversion_report['success']} 个文件夹")
            print(f"转换失败: {conversion_report['failed']} 个文件夹")
            
            if conversion_report['failed'] > 0:
                warning_msg = f"有 {conversion_report['failed']} 个文件夹未能找到对应的新路径"
                print(f"\n警告: {warning_msg}")
                messagebox.showwarning("警告", warning_msg)
                
            if not converted_map:
                error_msg = "没有找到任何可用的文件夹路径"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            # 更新路径映射
            self.folder_keyword_map = converted_map
            
            # 更新UI状态
            self.current_folder = next(iter(self.folder_keyword_map.keys()))
            self.current_keywords = self.folder_keyword_map[self.current_folder].copy()
            
            # 更新文件夹列表
            self.scanned_folders = list(self.folder_keyword_map.keys())
            self.ui_queue.put({
                'type': 'folder_list',
                'folders': self.scanned_folders
            })
            
            # 更新关键词显示
            keyword_text = ""
            for f, kws in self.folder_keyword_map.items():
                keyword_text += f"文件夹: {os.path.basename(f)}\n"
                keyword_text += "关键词:\n"
                for j, kw in enumerate(kws, 1):
                    keyword_text += f"  {j}. {kw}\n"
                keyword_text += "\n"
                
            self.ui_queue.put({
                'type': 'keyword_list',
                'text': keyword_text
            })
            
            # 更新当前处理信息
            self.update_current_info()
            self.scroll_to_current_keyword()
            
            # 更新按钮状态
            button_states = {
                'start_search': True,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True,
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 更新状态信息
            status_msg = f'已加载分析结果，共 {len(self.folder_keyword_map)} 个文件夹的关键词'
            print(f"\n{status_msg}")
            print(f"分析日期: {data['analysis_date']}")
            print(f"使用模型: {data.get('model', '未知')}")
            
            self.ui_queue.put({
                'type': 'status',
                'text': status_msg
            })
            
        except Exception as e:
            error_msg = f"加载分析结果失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)

    def setup_keyboard_listener(self):
        """设置键盘监听器"""
        def on_press(key):
            if key == Key.esc:
                if self.is_playing:
                    self.root.after(0, self.stop_search)
                return False
            return True
            
        def start_listener():
            with Listener(on_press=on_press) as listener:
                listener.join()
                
        if self.keyboard_listener is not None:
            self.keyboard_listener.join(timeout=1.0)
            
        self.keyboard_listener = threading.Thread(target=start_listener)
        self.keyboard_listener.daemon = True
        self.keyboard_listener.start()

    def update_ui(self):
        """处理UI更新队列"""
        try:
            while True:
                try:
                    update = self.ui_queue.get_nowait()
                    if update['type'] == 'status':
                        self.status_label.config(text=update['text'])
                        print(update['text'])
                    elif update['type'] == 'current_info':
                        self.current_info.delete('1.0', tk.END)
                        self.current_info.insert('1.0', update['text'])
                    elif update['type'] == 'current_keyword':
                        self.current_keyword_text.delete('1.0', tk.END)
                        self.current_keyword_text.insert('1.0', update['text'])
                    elif update['type'] == 'folder_list':
                        self.folder_listbox.delete(0, tk.END)
                        for folder in update['folders']:
                            self.folder_listbox.insert(tk.END, os.path.basename(folder))
                    elif update['type'] == 'keyword_list':
                        self.keyword_text.delete('1.0', tk.END)
                        self.keyword_text.insert('1.0', update['text'])
                    elif update['type'] == 'progress':
                        self.progress_label.config(text=update['text'])
                    elif update['type'] == 'button_states':
                        self.update_button_states(update['states'])
                except queue.Empty:
                    break
        finally:
            self.root.after(100, self.update_ui)

    def update_button_states(self, states):
        """更新按钮状态"""
        button_mapping = {
            'start_search': self.start_search_btn,
            'execute_download': self.execute_download_btn,
            'move_files': self.move_files_btn,
            'next_keyword': self.next_keyword_btn,
            'next_folder': self.next_folder_btn,
            'stop': self.stop_btn
        }
        
        for button_name, state in states.items():
            if button_name in button_mapping:
                button_mapping[button_name].config(state='normal' if state else 'disabled')

    def browse_folder(self):
        """浏览选择文件夹"""
        folder = filedialog.askdirectory(initialdir=self.default_path)
        if folder:
            self.path_entry.delete(0, tk.END)
            self.path_entry.insert(0, folder)
            print(f"已选择文件夹: {folder}")

    def load_record_file(self, record_type):
        """加载录制文件"""
        try:
            filename = filedialog.askopenfilename(
                initialdir=self.default_path,
                title=f"选择{record_type}录制文件",
                filetypes=[("JSON Files", "*.json")])
                
            if not filename:
                return
                
            with open(filename, 'r', encoding='utf-8') as f:
                actions = json.load(f)
                
            if record_type == 'search':
                self.recorded_actions_search = actions
                self.search_record_entry.delete(0, tk.END)
                self.search_record_entry.insert(0, filename)
            else:
                self.recorded_actions_download = actions
                self.download_record_entry.delete(0, tk.END)
                self.download_record_entry.insert(0, filename)
                
            print(f"已加载{record_type}录制文件: {filename}")
            
        except Exception as e:
            error_msg = f"加载文件失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)

    def save_analysis_results(self):
        """保存分析结果到文件"""
        try:
            if not self.folder_keyword_map:
                return
                
            # 生成文件名
            current_time = datetime.now()
            model_name = self.ai_model_var.get().replace(":", "_")
            filename = f"analysis_results_{current_time.strftime('%Y%m%d_%H%M%S')}_{len(self.folder_keyword_map)}folders_{model_name}.json"
            filepath = os.path.join(self.default_path, filename)
            
            # 准备保存数据
            data = {
                'analysis_date': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'model': self.ai_model_var.get(),
                'total_folders': len(self.folder_keyword_map),
                'keywords': self.folder_keyword_map
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
                
            print(f"分析结果已保存到: {filename}")
            return filepath
            
        except Exception as e:
            error_msg = f"保存分析结果失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return None

    def scan_folders(self, root_path: str) -> List[str]:
        """扫描指定目录下的Article文件夹"""
        try:
            article_folders = []
            for root, dirs, files in os.walk(root_path):
                for dir_name in dirs:
                    if dir_name.startswith("###Article"):
                        full_path = os.path.join(root, dir_name)
                        article_folders.append(full_path)
                        print(f"找到文章文件夹: {dir_name}")
                        
            print(f"共找到 {len(article_folders)} 个文件夹")
            return article_folders
            
        except Exception as e:
            print(f"扫描文件夹失败: {str(e)}")
            traceback.print_exc()
            return []

    def extract_titles(self, folder_path: str) -> List[str]:
        """提取文件夹中所有文本文件的第一行作为标题"""
        titles = []
        try:
            if not os.path.exists(folder_path):
                print(f"文件夹不存在: {folder_path}")
                return []

            for file in os.listdir(folder_path):
                if file.endswith(".txt"):
                    file_path = os.path.join(folder_path, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            first_line = f.readline().strip()
                            if first_line:
                                titles.append(first_line)
                                print(f"提取标题: {first_line}")
                    except Exception as e:
                        print(f"读取文件失败: {file}, 错误: {str(e)}")
                        continue

            if not titles:
                print(f"警告：在文件夹 {os.path.basename(folder_path)} 中未找到任何标题")
            else:
                print(f"成功提取 {len(titles)} 个标题")

            return titles

        except Exception as e:
            print(f"提取标题失败: {str(e)}")
            traceback.print_exc()
            return []

    def analyze_titles(self, titles: List[str]) -> Dict[str, List[str]]:
        """分析标题提取关键主题和信息"""
        try:
            print("正在分析标题内容...")
            if not self.available_models:
                print("警告：没有可用的本地模型，无法进行分析")
                return {}
                
            model = self.ai_model_var.get()
            if model not in self.available_models:
                print(f"警告：选择的模型 {model} 不可用，请选择其他模型")
                return {}
            
            titles_text = "\n".join(titles)
            analysis_prompt = f"""请仔细分析这组新闻标题，提取以下关键信息：

1. 核心主题：
   - 所有标题共同涉及的核心事件或内容
   - 按重要性排序
   - 每个主题用简短的词组表达

2. 关键实体：
   - 标题中出现的重要人物、产品、公司、组织等
   - 包括专有名词和重要通用名词
   - 按出现频率和重要性排序

3. 特征描述：
   - 能体现新闻特点的形容词、动词、场景
   - 突出视觉特征的词语
   - 能用于图片搜索的描述性词语

新闻标题:
{titles_text}

请分析并输出上述三类信息，每类信息用1-3个最重要的词组表示："""

            print("调用本地模型进行分析...")
            result = subprocess.run(
                ["ollama", "run", model],
                input=analysis_prompt,
                text=True,
                capture_output=True,
                encoding='utf-8',
                timeout=60
            )
            
            if result.returncode == 0 and result.stdout:
                analysis_result = {
                    'core_topic': [],
                    'key_entities': [],
                    'descriptors': []
                }
                
                current_section = None
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if '核心主题' in line:
                        current_section = 'core_topic'
                    elif '关键实体' in line:
                        current_section = 'key_entities'
                    elif '特征描述' in line or '重要描述' in line:
                        current_section = 'descriptors'
                    elif line and current_section and line[0] in '- *':
                        item = line.lstrip('- * ').strip()
                        if item and len(analysis_result[current_section]) < 3:
                            analysis_result[current_section].append(item)
                
                # 打印分析结果
                print("\n标题分析结果:")
                for key, values in analysis_result.items():
                    print(f"{key}: {', '.join(values)}")
                
                return analysis_result
            
            print("AI分析未返回有效结果")
            return {}
            
        except Exception as e:
            print(f"分析标题失败: {str(e)}")
            traceback.print_exc()
            return {}

    def generate_keywords(self, titles: List[str]) -> List[str]:
        """使用本地AI生成搜索关键词"""
        try:
            print("开始生成搜索关键词...")
            
            # 检查模型可用性
            if not self.available_models:
                print("警告：没有可用的本地模型，无法生成关键词")
                return self.generate_fallback_keywords()
                
            model = self.ai_model_var.get()
            if model not in self.available_models:
                print(f"警告：选择的模型 {model} 不可用，使用基本关键词生成方法")
                return self.generate_fallback_keywords()
            
            # 先进行标题分析
            analysis_result = self.analyze_titles(titles)
            if not analysis_result or not any(analysis_result.values()):
                print("标题分析失败，使用基本关键词生成方法")
                return self.generate_basic_keywords(titles)
            
            # 使用分析结果生成优化的关键词
            return self.generate_optimized_keywords(analysis_result, titles)
            
        except Exception as e:
            print(f"生成关键词失败: {str(e)}")
            traceback.print_exc()
            return self.generate_fallback_keywords()

    def generate_fallback_keywords(self) -> List[str]:
        """生成默认的备用关键词"""
        keywords = [f"标题关键词_{i+1}" for i in range(3)]
        print("使用默认关键词:", keywords)
        return keywords

    def generate_basic_keywords(self, titles: List[str]) -> List[str]:
        """基本的关键词生成方法"""
        try:
            if not self.available_models:
                return self.generate_fallback_keywords()
                
            model = self.ai_model_var.get()
            if model not in self.available_models:
                return self.generate_fallback_keywords()
                
            titles_text = "\n".join(titles)
            
            prompt = f"""请为以下新闻标题生成3个图片搜索关键词组合。

要求：
1. 第一个关键词组合：
   - 必须使用3-4个词
   - 必须包含最核心的实体和特征
   - 例如："特斯拉 工厂"，"iPhone 发布会"

2. 其他关键词：
   - 每组2-3个词
   - 不同于第一组的视角
   - 例如："生产线 车间"，"苹果 新品"

严格按如下格式输出3行：
1. 第一组关键词
2. 第二组关键词
3. 第三组关键词

新闻标题:
{titles_text}"""

            print("使用基本方法生成关键词...")
            result = subprocess.run(
                ["ollama", "run", model],
                input=prompt,
                text=True,
                capture_output=True,
                encoding='utf-8',
                timeout=60
            )
            
            if result.returncode == 0 and result.stdout:
                keywords = []
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if line and (line[0].isdigit() or line[0] == '-'):
                        keyword = line.lstrip('0123456789.- ').strip()
                        if keyword and len(keywords) < 3:
                            keywords.append(keyword)
                            print(f"生成基本关键词: {keyword}")
                
                # 确保至少返回3个关键词
                while len(keywords) < 3:
                    fallback = f"标题关键词_{len(keywords)+1}"
                    keywords.append(fallback)
                    print(f"添加默认关键词: {fallback}")
                
                return keywords[:3]
            
            print("无法生成有效的关键词")
            return self.generate_fallback_keywords()
            
        except Exception as e:
            print(f"基本关键词生成失败: {str(e)}")
            traceback.print_exc()
            return self.generate_fallback_keywords()

    def generate_optimized_keywords(self, analysis: Dict[str, List[str]], titles: List[str], max_retries: int = 3) -> List[str]:
        """基于分析结果生成优化的关键词"""
        try:
            if not self.available_models:
                return self.generate_fallback_keywords()
                
            model = self.ai_model_var.get()
            if model not in self.available_models:
                return self.generate_fallback_keywords()
                
            titles_text = "\n".join(titles)
            retry_count = 0
            
            while retry_count < max_retries:
                # 构建更详细的提示词
                prompt = f"""基于以下标题分析结果，生成3个优化的图片搜索关键词组合。
                
已提取的关键信息:
核心主题: {', '.join(analysis.get('core_topic', []))}
关键实体: {', '.join(analysis.get('key_entities', []))}
特征描述: {', '.join(analysis.get('descriptors', []))}

原始标题:
{titles_text}

请生成3个搜索关键词组合：

1. 第一个关键词组合（最重要）：
   - 必须至少包含2个词
   - 首个词必须是最主要的实体或主题
   - 第二个词必须是最重要的特征或描述
   - 可以加第三个词来补充细节
   - 整体必须准确体现标题的核心内容

2. 第二和第三个关键词组合：
   - 每组2-4个词
   - 提供不同的搜索视角
   - 可以使用其他主题和描述词
   - 避免与第一组重复

其他要求：
- 每组关键词都要简洁有力
- 确保每组关键词间有明显区别
- 严格按数字编号输出，例如：
1. 关键词组合一
2. 关键词组合二
3. 关键词组合三"""

                print(f"尝试生成关键词 (第 {retry_count + 1} 次)")
                result = subprocess.run(
                    ["ollama", "run", model],
                    input=prompt,
                    text=True,
                    capture_output=True,
                    encoding='utf-8',
                    timeout=60
                )
                
                if result.returncode == 0 and result.stdout:
                    keywords = []
                    for line in result.stdout.split('\n'):
                        line = line.strip()
                        if line and (line[0].isdigit() or line[0] == '-'):
                            keyword = line.lstrip('0123456789.- ').strip()
                            if keyword and len(keywords) < 3:
                                keywords.append(keyword)
                                print(f"生成优化关键词: {keyword}")
                    
                    # 验证关键词质量
                    if self.validate_keywords(keywords):
                        return keywords
                    
                retry_count += 1
                print(f"关键词质量不满足要求，准备重试 ({retry_count}/{max_retries})")
            
            print(f"达到最大重试次数 {max_retries}，回退到基本关键词生成方法")
            return self.generate_basic_keywords(titles)
            
        except Exception as e:
            print(f"生成优化关键词失败: {str(e)}")
            traceback.print_exc()
            return self.generate_basic_keywords(titles)

    def scroll_to_current_keyword(self):
        """滚动到当前关键词位置"""
        try:
            if self.current_folder and self.current_keywords:
                # 构建目标文本模式
                folder_name = os.path.basename(self.current_folder)
                current_keyword = self.current_keywords[0]
                
                # 获取文本内容
                content = self.keyword_text.get("1.0", tk.END)
                lines = content.split('\n')
                
                # 查找当前文件夹和关键词的位置
                target_line = 0
                for i, line in enumerate(lines):
                    if folder_name in line:
                        target_line = i + 2  # 跳过"关键词:"行
                        break
                
                # 滚动到目标位置
                if target_line > 0:
                    self.keyword_text.see(f"{target_line}.0")
                    
                    # 清除之前的标记
                    self.keyword_text.tag_remove("highlight", "1.0", tk.END)
                    
                    # 高亮当前关键词所在行
                    line_start = f"{target_line}.0"
                    line_end = f"{target_line + 1}.0"
                    self.keyword_text.tag_add("highlight", line_start, line_end)
                    self.keyword_text.tag_config("highlight", background=ModernDarkTheme.HIGHLIGHT_BG,
                                               foreground=ModernDarkTheme.HIGHLIGHT_FG)
                    
        except Exception as e:
            print(f"滚动到当前关键词失败: {str(e)}")
            traceback.print_exc()

    def validate_keywords(self, keywords: List[str]) -> bool:
        """验证关键词的质量"""
        if not keywords or len(keywords) != 3:
            print("关键词数量不正确")
            return False
            
        # 验证第一个关键词
        first_words = keywords[0].split()
        if len(first_words) < 2:
            print("第一个关键词组合词数不足")
            return False
            
        # 验证关键词间的差异性
        word_sets = [set(kw.split()) for kw in keywords]
        for i in range(len(word_sets)):
            for j in range(i + 1, len(word_sets)):
                # 计算重叠程度
                overlap = len(word_sets[i] & word_sets[j])
                if overlap > min(len(word_sets[i]), len(word_sets[j])) // 2:
                    print(f"关键词 {i+1} 和 {j+1} 重复度过高")
                    return False
                    
        # 验证每个关键词的基本要求
        for i, kw in enumerate(keywords, 1):
            words = kw.split()
            if len(words) < 2 or len(words) > 4:
                print(f"关键词 {i} 的词数不符合要求")
                return False
            if any(len(word) < 2 for word in words):  # 避免单字词
                print(f"关键词 {i} 含有过短的词")
                return False
                
        return True

    def start_analysis(self):
        """开始AI分析流程"""
        try:
            if not self.scanned_folders:
                error_msg = "请先加载文件夹"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return False
                
            # 检查模型可用性
            if not self.available_models:
                error_msg = "没有可用的本地模型，无法进行AI分析"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return False
                
            selected_model = self.ai_model_var.get()
            if selected_model not in self.available_models:
                error_msg = f"选择的模型 {selected_model} 不可用，请选择其他模型"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return False
                
            # 清空之前的数据
            self.folder_keyword_map.clear()
            print("开始AI分析...")
            
            # 分析进度提示
            total_folders = len(self.scanned_folders)
            
            # 为每个文件夹生成关键词
            for i, folder in enumerate(self.scanned_folders, 1):
                # 更新状态
                progress_msg = f'正在分析第 {i}/{total_folders} 个文件夹: {os.path.basename(folder)}'
                print(progress_msg)
                self.ui_queue.put({
                    'type': 'status',
                    'text': progress_msg
                })
                
                titles = self.extract_titles(folder)
                if titles:
                    print(f"\n正在为文件夹 {os.path.basename(folder)} 生成关键词")
                    print(f"标题列表:\n{chr(10).join(titles)}\n")
                    
                    keywords = self.generate_keywords(titles)
                    if keywords:
                        self.folder_keyword_map[folder] = keywords
                        print(f"生成的关键词: {', '.join(keywords)}\n")
                        
                # 更新关键词显示
                keyword_text = ""
                for f, kws in self.folder_keyword_map.items():
                    keyword_text += f"文件夹: {os.path.basename(f)}\n"
                    keyword_text += "关键词:\n"
                    for j, kw in enumerate(kws, 1):
                        keyword_text += f"  {j}. {kw}\n"
                    keyword_text += "\n"
                    
                self.ui_queue.put({
                    'type': 'keyword_list',
                    'text': keyword_text
                })
                
                # 更新进度
                self.ui_queue.put({
                    'type': 'progress',
                    'text': f"分析进度: {i}/{total_folders}"
                })
            
            # AI分析完成后的初始化
            if self.folder_keyword_map:
                # 保存分析结果
                result_file = self.save_analysis_results()
                if result_file:
                    print(f"分析结果已保存到: {os.path.basename(result_file)}")
                
                # 设置第一个文件夹为当前文件夹
                self.current_folder = next(iter(self.folder_keyword_map.keys()))
                self.current_keywords = self.folder_keyword_map[self.current_folder].copy()
                
                # 立即更新剪贴板为第一个关键词
                if self.current_keywords:
                    current_keyword = self.current_keywords[0]
                    self.root.clipboard_clear()
                    self.root.clipboard_append(current_keyword)
                    print(f"设置初始关键词: {current_keyword}")
                    
                # 更新当前处理信息显示并滚动到当前关键词
                self.update_current_info()
                self.scroll_to_current_keyword()
            
            complete_msg = f'AI分析完成，共生成 {len(self.folder_keyword_map)} 个文件夹的关键词'
            if result_file:
                complete_msg += f'，结果已保存'
            print(complete_msg)
            self.ui_queue.put({
                'type': 'status',
                'text': complete_msg
            })
            
            # 清空进度显示
            self.ui_queue.put({
                'type': 'progress',
                'text': ""
            })
            
            # 更新按钮状态
            button_states = {
                'start_search': True,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True,  # 允许切换关键词
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            return True
            
        except Exception as e:
            error_msg = f"AI分析过程出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            return False

    def execute_download(self):
        """执行下载动作"""
        try:
            if not self.recorded_actions_download:
                error_msg = "请先加载下载录制文件"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            # 更新状态
            self.current_state = "downloading"
            
            # 开始监控下载文件夹
            self.start_download_monitoring()
            
            # 禁用相关按钮，但保持移动文件按钮可用
            button_states = {
                'start_search': False,
                'execute_download': False,
                'move_files': False,  # 下载过程中暂时禁用
                'next_keyword': True,  # 保持关键词切换可用
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 启动下载线程
            self.is_playing = True
            threading.Thread(target=self.download_thread, daemon=True).start()
            
        except Exception as e:
            error_msg = f"启动下载失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            self.stop_download_monitoring()

    def start_download_monitoring(self):
        """开始监控下载文件夹 - 增强版"""
        try:
            with self.monitoring_lock:
                if self.is_monitoring:
                    return
                    
                print("开始监控下载文件夹...")
                self.download_start_time = time.time()
                self.monitored_files.clear()
                self.is_monitoring = True
                self.early_stop_monitoring = False
                self.monitor_remaining_time = 120  # 2分钟
                
                # 记录监控开始时的现有文件
                existing_files = self.get_image_files(self.download_dir)
                print(f"当前下载文件夹中已有 {len(existing_files)} 个图片文件")
                
                # 启动监控线程
                self.monitor_thread = threading.Thread(
                    target=self.monitor_downloads,
                    daemon=True
                )
                self.monitor_thread.start()
                
                # 启动进度更新
                self.update_monitor_progress()
                
                # 更新按钮状态
                self.stop_monitor_btn.config(state='normal')
                
        except Exception as e:
            print(f"启动下载监控失败: {str(e)}")
            traceback.print_exc()     

    def update_monitor_progress(self):
        """更新监控进度"""
        if self.is_monitoring:
            elapsed = time.time() - self.download_start_time
            self.monitor_remaining_time = max(0, 120 - int(elapsed))
            
            # 更新进度显示
            progress_text = (
                f"监控中 ({self.monitor_remaining_time}秒) - "
                f"已检测到 {len(self.monitored_files)} 个新文件"
            )
            self.ui_queue.put({
                'type': 'progress',
                'text': progress_text
            })
            
            # 继续更新直到监控结束
            if self.is_monitoring:
                self.root.after(self.monitor_update_interval, self.update_monitor_progress)           

    def stop_monitoring_early(self):
        """提前停止监控"""
        if self.is_monitoring:
            print("手动停止下载监控")
            self.early_stop_monitoring = True
            self.stop_monitor_btn.config(state='disabled')
            
            # 等待监控实际停止
            self.stop_download_monitoring()
            
            # 更新UI状态
            self.update_ui_after_action()                 

    def stop_download_monitoring(self):
        """停止监控下载文件夹"""
        with self.monitoring_lock:
            if not self.is_monitoring:
                return
                
            print("停止下载文件夹监控")
            self.is_monitoring = False
            
            if self.monitor_thread:
                self.monitor_thread.join(timeout=1.0)
                self.monitor_thread = None       

    def get_image_files(self, folder_path: str) -> set:
        """获取文件夹中的图片文件"""
        supported_formats = ('.png', '.jpg', '.jpeg', '.gif', '.webp', '.jfif')
        image_files = set()
        
        try:
            for file in os.listdir(folder_path):
                if file.lower().endswith(supported_formats):
                    image_files.add(os.path.join(folder_path, file))
        except Exception as e:
            print(f"获取图片文件列表失败: {str(e)}")
            
        return image_files        

    def monitor_downloads(self):
        """监控下载文件夹的线程函数 - 增强版"""
        try:
            initial_files = self.get_image_files(self.download_dir)
            
            while self.is_monitoring:
                if self.early_stop_monitoring:
                    print("监控被手动停止")
                    break
                    
                time.sleep(0.5)  # 降低CPU使用率
                
                # 获取当前文件列表
                current_files = self.get_image_files(self.download_dir)
                
                # 检测新文件
                new_files = current_files - initial_files
                if new_files:
                    for file_path in new_files:
                        if os.path.getctime(file_path) >= self.download_start_time:
                            self.monitored_files.add(file_path)
                            print(f"检测到新下载的图片: {os.path.basename(file_path)}")
                
                # 检查是否达到监控时间限制
                if time.time() - self.download_start_time > 120:
                    print("下载监控时间达到2分钟，自动停止监控")
                    break
                    
        except Exception as e:
            print(f"下载监控过程出错: {str(e)}")
            traceback.print_exc()
        finally:
            self.is_monitoring = False
            self.stop_monitor_btn.config(state='disabled')

    def download_thread(self):
        """下载线程"""
        try:
            print("开始执行下载动作...")
            self.execute_record(self.recorded_actions_download)
            if not self.is_playing:
                return
                
            # 等待一段时间确保下载完成
            print("等待下载完成...")
            time.sleep(5)
            
            # 更新界面状态
            self.update_ui_after_action()
            
        except Exception as e:
            error_msg = f"下载过程出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()

    def next_keyword(self):
        """处理下一个关键词"""
        try:
            if self.current_keywords:
                print("准备处理下一个关键词...")
                
                # 停止当前的下载监控
                self.stop_download_monitoring()
                
                # 清理监控记录
                self.monitored_files.clear()
                
                # 移除当前关键词
                self.current_keywords.pop(0)
                
                if self.current_keywords:
                    # 还有更多关键词要处理
                    self.is_playing = False  # 重置播放状态
                    self.current_state = "ready"
                    
                    # 立即更新剪贴板为新的关键词
                    current_keyword = self.current_keywords[0]
                    self.root.clipboard_clear()
                    self.root.clipboard_append(current_keyword)
                    print(f"更新剪贴板关键词: {current_keyword}")
                    
                    # 保存当前状态
                    self.save_current_state()
                    
                    # 更新当前处理信息并滚动到当前关键词
                    self.update_current_info()
                    self.scroll_to_current_keyword()
                    
                    button_states = {
                        'start_search': True,
                        'execute_download': False,
                        'move_files': False,
                        'next_keyword': True,  # 保持关键词切换可用
                        'next_folder': True,
                        'stop': True
                    }
                    
                    self.ui_queue.put({
                        'type': 'button_states',
                        'states': button_states
                    })
                    
                    status_msg = '已切换到下一个关键词，请点击"开始搜索"继续，或继续切换关键词'
                    print(status_msg)
                    self.ui_queue.put({
                        'type': 'status',
                        'text': status_msg
                    })
                    
                else:
                    status_msg = '当前文件夹的所有关键词已处理完成，请选择"下一文件夹"继续'
                    print(status_msg)
                    self.ui_queue.put({
                        'type': 'status',
                        'text': status_msg
                    })
                    
                    # 更新按钮状态
                    button_states = {
                        'start_search': False,
                        'execute_download': False,
                        'move_files': False,
                        'next_keyword': False,
                        'next_folder': True,
                        'stop': True
                    }
                    
                    self.ui_queue.put({
                        'type': 'button_states',
                        'states': button_states
                    })
                    
        except Exception as e:
            error_msg = f"处理下一关键词失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            
            # 发生错误时恢复按钮状态
            button_states = {
                'start_search': True if self.current_keywords else False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True if len(self.current_keywords) > 1 else False,
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })

    def next_folder(self):
        """处理下一个文件夹"""
        try:
            # 停止当前的下载监控
            self.stop_download_monitoring()
            
            # 清理监控记录
            self.monitored_files.clear()
            
            folders = list(self.folder_keyword_map.keys())
            current_index = folders.index(self.current_folder)
            
            if current_index + 1 < len(folders):
                # 还有更多文件夹要处理
                print("开始处理下一个文件夹...")
                self.current_folder = folders[current_index + 1]
                self.current_keywords = self.folder_keyword_map[self.current_folder].copy()
                
                # 立即更新剪贴板为新文件夹的第一个关键词
                if self.current_keywords:
                    current_keyword = self.current_keywords[0]
                    self.root.clipboard_clear()
                    self.root.clipboard_append(current_keyword)
                    print(f"更新剪贴板关键词: {current_keyword}")
                
                # 更新文件夹列表选中项
                folder_name = os.path.basename(self.current_folder)
                for i in range(self.folder_listbox.size()):
                    if self.folder_listbox.get(i) == folder_name:
                        self.folder_listbox.selection_clear(0, tk.END)
                        self.folder_listbox.selection_set(i)
                        self.folder_listbox.see(i)
                        break
                
                self.is_playing = False  # 重置播放状态
                self.current_state = "ready"
                
                # 保存当前状态
                self.save_current_state()
                
                # 更新当前处理信息并滚动到当前关键词
                self.update_current_info()
                self.scroll_to_current_keyword()
                
                # 更新按钮状态
                button_states = {
                    'start_search': True,
                    'execute_download': False,
                    'move_files': False,
                    'next_keyword': True,  # 允许切换关键词
                    'next_folder': True,
                    'stop': True
                }
                
                self.ui_queue.put({
                    'type': 'button_states',
                    'states': button_states
                })
                
                status_msg = f'已切换到新文件夹: {os.path.basename(self.current_folder)}，请点击"开始搜索"继续，或切换关键词'
                print(status_msg)
                self.ui_queue.put({
                    'type': 'status',
                    'text': status_msg
                })
                
            else:
                status_msg = '所有文件夹已处理完成'
                print(status_msg)
                self.ui_queue.put({
                    'type': 'status',
                    'text': status_msg
                })
                
                # 更新按钮状态
                button_states = {
                    'start_search': False,
                    'execute_download': False,
                    'move_files': False,
                    'next_keyword': False,
                    'next_folder': False,
                    'stop': False
                }
                
                self.ui_queue.put({
                    'type': 'button_states',
                    'states': button_states
                })
                
        except Exception as e:
            error_msg = f"处理下一文件夹失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)

    def update_ui_after_action(self):
        """根据当前状态更新UI"""
        if self.current_state == "searching":
            # 搜索完成后，同时允许执行下载或直接移动文件
            self.current_state = "search_complete"
            button_states = {
                'start_search': False,
                'execute_download': True,   # 可选的自动下载
                'move_files': True,         # 允许直接移动文件
                'next_keyword': True,       # 保持关键词切换可用
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 更新状态消息
            self.ui_queue.put({
                'type': 'status',
                'text': '搜索完成，您可以:\n1. 点击"执行下载"自动下载\n2. 手动下载后直接点击"移动文件"\n3. 选择"下一关键词"/"下一文件夹"跳过'
            })
            
        elif self.current_state == "downloading":
            # 下载完成后
            self.current_state = "download_complete"
            self.stop_download_monitoring()  # 确保停止监控
            
            button_states = {
                'start_search': False,
                'execute_download': False,
                'move_files': True,
                'next_keyword': True,
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 更新状态消息
            monitored_count = len(self.monitored_files)
            status_msg = f'下载完成，检测到 {monitored_count} 个新文件，请点击"移动文件"继续'
            self.ui_queue.put({
                'type': 'status',
                'text': status_msg
            })            

    def start_search(self):
        """开始搜索流程"""
        try:
            if not self.recorded_actions_search or not self.recorded_actions_download:
                error_msg = "请先加载所有必要的录制文件"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            if not self.folder_keyword_map:
                error_msg = "请先完成AI分析生成关键词"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            # 确保状态正确
            if not self.current_keywords:
                error_msg = "当前没有可处理的关键词"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return
                
            # 在开始搜索前更新剪贴板
            current_keyword = self.current_keywords[0]
            self.root.clipboard_clear()
            self.root.clipboard_append(current_keyword)
            print(f"更新剪贴板关键词: {current_keyword}")
            
            self.is_playing = True
            self.current_state = "searching"
            
            # 保存当前状态
            self.save_current_state()
            
            # 更新当前处理信息并滚动到当前关键词
            self.update_current_info()
            self.scroll_to_current_keyword()
            
            # 禁用相关按钮
            button_states = {
                'start_search': False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True,  # 保持关键词切换可用
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 启动搜索线程
            threading.Thread(target=self.search_thread, daemon=True).start()
            
            start_msg = f'开始处理文件夹: {os.path.basename(self.current_folder)}'
            print(start_msg)
            self.ui_queue.put({
                'type': 'status',
                'text': start_msg
            })
            
        except Exception as e:
            error_msg = f"启动搜索失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            
            # 发生错误时恢复按钮状态
            button_states = {
                'start_search': True if self.current_keywords else False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True if len(self.current_keywords) > 1 else False,
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })

    def update_current_info(self):
        """更新当前处理信息"""
        if self.current_folder and self.current_keywords:
            folder_info = f"当前文件夹: {os.path.basename(self.current_folder)}\n"
            folder_info += f"剩余关键词数: {len(self.current_keywords)}\n"
            folder_info += f"总文件夹数: {len(self.folder_keyword_map)}"
            
            current_keyword = self.current_keywords[0] if self.current_keywords else ""
            
            self.ui_queue.put({
                'type': 'current_info',
                'text': folder_info
            })
            
            self.ui_queue.put({
                'type': 'current_keyword',
                'text': current_keyword
            })

    def save_current_state(self):
        """保存当前状态"""
        self.last_state = {
            'current_folder': self.current_folder,
            'current_keywords': self.current_keywords.copy() if self.current_keywords else [],
            'is_playing': self.is_playing,
            'current_state': self.current_state
        }
        print("保存当前状态")

    def restore_last_state(self):
        """恢复到上一步状态"""
        if self.last_state:
            self.current_folder = self.last_state['current_folder']
            self.current_keywords = self.last_state['current_keywords'].copy()
            self.is_playing = False
            self.current_state = self.last_state['current_state']
            self.update_current_info()
            print("已恢复到上一步状态")

    def stop_search(self):
        """停止搜索过程"""
        self.is_playing = False
        self.current_state = "ready"
        
        # 恢复到上一步状态
        self.restore_last_state()
        
        status_msg = '操作已停止'
        print(status_msg)
        self.ui_queue.put({
            'type': 'status',
            'text': status_msg
        })
        
        # 更新按钮状态
        button_states = {
            'start_search': True,
            'execute_download': False,
            'move_files': False,
            'next_keyword': True,  # 保持关键词切换可用
            'next_folder': True,
            'stop': True
        }
        
        self.ui_queue.put({
            'type': 'button_states',
            'states': button_states
        })

    def search_thread(self):
        """搜索线程的主要逻辑"""
        try:
            if not self.current_keywords:
                status_msg = '当前文件夹的关键词已处理完成'
                print(status_msg)
                self.ui_queue.put({
                    'type': 'status',
                    'text': status_msg
                })
                return
                
            current_keyword = self.current_keywords[0]
            print(f"准备搜索关键词: {current_keyword}")
            
            # 更新当前处理信息并滚动到当前关键词
            self.update_current_info()
            self.scroll_to_current_keyword()
            
            # 执行搜索动作
            print("开始执行搜索动作...")
            self.execute_record(self.recorded_actions_search)
            if not self.is_playing:
                return
                
            # 更新状态并等待用户选择后续操作
            wait_msg = '搜索完成，您可以:\n1. 点击"执行下载"自动下载\n2. 手动下载后直接点击"移动文件"\n3. 选择"下一关键词"/"下一文件夹"跳过'
            print(wait_msg)
            self.ui_queue.put({
                'type': 'status',
                'text': wait_msg
            })
            
            # 更新按钮状态 - 同时启用下载和移动文件按钮
            button_states = {
                'start_search': False,
                'execute_download': True,  # 可选的自动下载
                'move_files': True,        # 允许直接移动文件
                'next_keyword': True,      # 保持关键词切换可用
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
        except Exception as e:
            error_msg = f"搜索过程出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            
            # 发生错误时恢复按钮状态
            button_states = {
                'start_search': True if self.current_keywords else False,
                'execute_download': False,
                'move_files': False,
                'next_keyword': True if len(self.current_keywords) > 1 else False,
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })

    def move_downloaded_files(self):
        """移动下载的文件到目标文件夹 - 增强版带重试"""
        try:
            if not self.current_folder:
                print("错误：未设置当前处理文件夹")
                return
                
            if not self.monitored_files:
                print("警告：没有检测到需要移动的新文件")
                messagebox.showwarning("警告", "没有检测到需要移动的新文件")
                return
            
            # 保存当前状态到历史栈
            self.push_history()
            
            # 禁用按钮
            self.update_button_states_for_state('downloading')
            
            print(f"开始移动文件到: {self.current_folder}")
            
            # 确保目标文件夹存在
            if not os.path.exists(self.current_folder):
                os.makedirs(self.current_folder)
                print(f"创建目标文件夹: {self.current_folder}")
            
            # 移动监控到的文件
            moved_files = []
            failed_files = []
            
            for source_path in self.monitored_files:
                success = False
                retry_count = 0
                
                while not success and retry_count < self.max_move_retries:
                    try:
                        if os.path.exists(source_path):
                            file_name = os.path.basename(source_path)
                            target_path = os.path.join(self.current_folder, file_name)
                            
                            # 处理文件名冲突
                            counter = 1
                            while os.path.exists(target_path):
                                name, ext = os.path.splitext(file_name)
                                target_path = os.path.join(self.current_folder, f"{name}_{counter}{ext}")
                                counter += 1
                            
                            shutil.move(source_path, target_path)
                            moved_files.append(file_name)
                            success = True
                            print(f"已移动文件: {file_name} -> {os.path.basename(target_path)}")
                            
                        else:
                            print(f"文件不存在: {source_path}")
                            failed_files.append(os.path.basename(source_path))
                            break
                            
                    except Exception as e:
                        retry_count += 1
                        if retry_count < self.max_move_retries:
                            print(f"移动文件失败，将在 {self.retry_delay} 秒后重试: {str(e)}")
                            time.sleep(self.retry_delay)
                        else:
                            print(f"移动文件失败，达到最大重试次数: {str(e)}")
                            failed_files.append(os.path.basename(source_path))
            
            # 清理监控记录
            self.monitored_files.clear()
            
            # 更新状态
            status_msg = f'已成功移动 {len(moved_files)} 个文件'
            if failed_files:
                status_msg += f'，{len(failed_files)} 个文件移动失败'
                print("失败的文件:", failed_files)
            
            print(status_msg)
            self.ui_queue.put({
                'type': 'status',
                'text': status_msg
            })
            
            # 更新按钮状态
            self.update_button_states_for_state('ready')
            
        except Exception as e:
            error_msg = f"移动文件失败: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            self.update_button_states_for_state('download_complete')

    def update_ui_after_action(self):
        """根据当前状态更新UI"""
        if self.current_state == "searching":
            # 搜索完成后，同时允许执行下载或直接移动文件
            self.current_state = "search_complete"
            button_states = {
                'start_search': False,
                'execute_download': True,   # 可选的自动下载
                'move_files': True,         # 允许直接移动文件
                'next_keyword': True,       # 保持关键词切换可用
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 更新状态消息
            self.ui_queue.put({
                'type': 'status',
                'text': '搜索完成，您可以:\n1. 点击"执行下载"自动下载\n2. 手动下载后直接点击"移动文件"\n3. 选择"下一关键词"/"下一文件夹"跳过'
            })
            
        elif self.current_state == "downloading":
            # 下载完成后
            self.current_state = "download_complete"
            button_states = {
                'start_search': False,
                'execute_download': False,
                'move_files': True,
                'next_keyword': True,
                'next_folder': True,
                'stop': True
            }
            
            self.ui_queue.put({
                'type': 'button_states',
                'states': button_states
            })
            
            # 更新状态消息
            self.ui_queue.put({
                'type': 'status',
                'text': '下载完成，请点击"移动文件"继续'
            })

    def run(self):
        """运行应用程序"""
        try:
            print("启动应用程序...")
            self.root.mainloop()
        except Exception as e:
            print(f"应用程序运行出错: {str(e)}")
            traceback.print_exc()
        finally:
            if self.keyboard_listener:
                self.keyboard_listener.join(timeout=1.0)

def main():
    try:
        print("初始化应用程序...")
        app = ImageSearchAutomation()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        traceback.print_exc()
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()