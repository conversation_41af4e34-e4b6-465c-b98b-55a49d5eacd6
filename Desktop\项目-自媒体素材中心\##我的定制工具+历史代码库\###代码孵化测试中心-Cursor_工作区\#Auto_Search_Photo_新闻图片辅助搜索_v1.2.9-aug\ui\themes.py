"""
Mac风格主题设计
优雅、简洁、现代的视觉设计
"""

from typing import Dict, Any
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QColor, QPalette, QFont
from PyQt6.QtWidgets import QApplication


class MacTheme:
    """Mac风格主题配置"""
    
    # 基础颜色 - 深色模式
    BACKGROUND_PRIMARY = "#1c1c1e"      # 主背景色
    BACKGROUND_SECONDARY = "#2c2c2e"    # 次要背景色
    BACKGROUND_TERTIARY = "#3a3a3c"     # 第三级背景色
    
    # 表面颜色
    SURFACE_PRIMARY = "#2c2c2e"         # 主表面色
    SURFACE_SECONDARY = "#3a3a3c"       # 次要表面色
    SURFACE_ELEVATED = "#48484a"        # 提升表面色
    
    # 文本颜色
    TEXT_PRIMARY = "#ffffff"            # 主文本色
    TEXT_SECONDARY = "#ebebf5"          # 次要文本色
    TEXT_TERTIARY = "#ebebf599"         # 第三级文本色
    TEXT_DISABLED = "#ebebf54d"         # 禁用文本色
    
    # 强调色
    ACCENT_BLUE = "#007aff"             # 蓝色强调
    ACCENT_GREEN = "#30d158"            # 绿色强调
    ACCENT_ORANGE = "#ff9f0a"           # 橙色强调
    ACCENT_RED = "#ff453a"              # 红色强调
    ACCENT_PURPLE = "#bf5af2"           # 紫色强调
    
    # 系统颜色
    SEPARATOR = "#38383a"               # 分隔线
    BORDER = "#48484a"                  # 边框
    SHADOW = "#00000040"                # 阴影
    
    # 状态颜色
    SUCCESS = "#30d158"                 # 成功
    WARNING = "#ff9f0a"                 # 警告
    ERROR = "#ff453a"                   # 错误
    INFO = "#007aff"                    # 信息
    
    # 透明度
    OPACITY_DISABLED = 0.3
    OPACITY_SECONDARY = 0.6
    OPACITY_TERTIARY = 0.4
    
    # 圆角半径
    RADIUS_SMALL = 6
    RADIUS_MEDIUM = 8
    RADIUS_LARGE = 12
    RADIUS_XLARGE = 16
    
    # 间距
    SPACING_XS = 4
    SPACING_SM = 8
    SPACING_MD = 12
    SPACING_LG = 16
    SPACING_XL = 24
    SPACING_XXL = 32
    
    # 字体
    FONT_FAMILY = "SF Pro Display"
    FONT_SIZE_SMALL = 11
    FONT_SIZE_BODY = 13
    FONT_SIZE_TITLE = 16
    FONT_SIZE_LARGE_TITLE = 20
    FONT_SIZE_HEADLINE = 24
    
    @classmethod
    def get_color(cls, color_name: str) -> QColor:
        """获取颜色对象"""
        color_value = getattr(cls, color_name, "#ffffff")
        return QColor(color_value)
    
    @classmethod
    def get_font(cls, size: int = None, weight: int = QFont.Weight.Normal) -> QFont:
        """获取字体对象"""
        font = QFont(cls.FONT_FAMILY)
        font.setPixelSize(size or cls.FONT_SIZE_BODY)
        font.setWeight(weight)
        return font


class ThemeManager:
    """主题管理器"""
    
    def __init__(self):
        self.current_theme = MacTheme()
        self._setup_application_palette()
    
    def _setup_application_palette(self):
        """设置应用程序调色板"""
        app = QApplication.instance()
        if not app:
            return
        
        palette = QPalette()
        
        # 设置基础颜色
        palette.setColor(QPalette.ColorRole.Window, self.current_theme.get_color("BACKGROUND_PRIMARY"))
        palette.setColor(QPalette.ColorRole.WindowText, self.current_theme.get_color("TEXT_PRIMARY"))
        palette.setColor(QPalette.ColorRole.Base, self.current_theme.get_color("SURFACE_PRIMARY"))
        palette.setColor(QPalette.ColorRole.AlternateBase, self.current_theme.get_color("SURFACE_SECONDARY"))
        palette.setColor(QPalette.ColorRole.Text, self.current_theme.get_color("TEXT_PRIMARY"))
        palette.setColor(QPalette.ColorRole.Button, self.current_theme.get_color("SURFACE_PRIMARY"))
        palette.setColor(QPalette.ColorRole.ButtonText, self.current_theme.get_color("TEXT_PRIMARY"))
        palette.setColor(QPalette.ColorRole.Highlight, self.current_theme.get_color("ACCENT_BLUE"))
        palette.setColor(QPalette.ColorRole.HighlightedText, self.current_theme.get_color("TEXT_PRIMARY"))
        
        app.setPalette(palette)
    
    def get_stylesheet(self) -> str:
        """获取全局样式表"""
        return f"""
        /* 全局样式 */
        QWidget {{
            background-color: {self.current_theme.BACKGROUND_PRIMARY};
            color: {self.current_theme.TEXT_PRIMARY};
            font-family: "{self.current_theme.FONT_FAMILY}";
            font-size: {self.current_theme.FONT_SIZE_BODY}px;
        }}
        
        /* 主窗口 */
        QMainWindow {{
            background-color: {self.current_theme.BACKGROUND_PRIMARY};
            border: none;
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_MEDIUM}px;
            padding: {self.current_theme.SPACING_SM}px {self.current_theme.SPACING_MD}px;
            color: {self.current_theme.TEXT_PRIMARY};
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {self.current_theme.SURFACE_SECONDARY};
            border-color: {self.current_theme.ACCENT_BLUE};
        }}
        
        QPushButton:pressed {{
            background-color: {self.current_theme.SURFACE_ELEVATED};
        }}
        
        QPushButton:disabled {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            color: {self.current_theme.TEXT_DISABLED};
            border-color: {self.current_theme.SEPARATOR};
        }}
        
        /* 主要按钮 */
        QPushButton[primary="true"] {{
            background-color: {self.current_theme.ACCENT_BLUE};
            border-color: {self.current_theme.ACCENT_BLUE};
            color: white;
        }}
        
        QPushButton[primary="true"]:hover {{
            background-color: #0056cc;
        }}
        
        /* 危险按钮 */
        QPushButton[danger="true"] {{
            background-color: {self.current_theme.ERROR};
            border-color: {self.current_theme.ERROR};
            color: white;
        }}
        
        QPushButton[danger="true"]:hover {{
            background-color: #d70015;
        }}
        
        /* 成功按钮 */
        QPushButton[success="true"] {{
            background-color: {self.current_theme.SUCCESS};
            border-color: {self.current_theme.SUCCESS};
            color: white;
        }}
        
        QPushButton[success="true"]:hover {{
            background-color: #28a745;
        }}
        
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_MEDIUM}px;
            padding: {self.current_theme.SPACING_SM}px;
            color: {self.current_theme.TEXT_PRIMARY};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {self.current_theme.ACCENT_BLUE};
            outline: none;
        }}
        
        /* 列表样式 */
        QListWidget {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_MEDIUM}px;
            outline: none;
        }}
        
        QListWidget::item {{
            padding: {self.current_theme.SPACING_SM}px;
            border-bottom: 1px solid {self.current_theme.SEPARATOR};
        }}
        
        QListWidget::item:selected {{
            background-color: {self.current_theme.ACCENT_BLUE};
            color: white;
        }}
        
        QListWidget::item:hover {{
            background-color: {self.current_theme.SURFACE_SECONDARY};
        }}
        
        /* 标签样式 */
        QLabel {{
            color: {self.current_theme.TEXT_PRIMARY};
            background: transparent;
        }}
        
        QLabel[secondary="true"] {{
            color: {self.current_theme.TEXT_SECONDARY};
        }}
        
        QLabel[tertiary="true"] {{
            color: {self.current_theme.TEXT_TERTIARY};
        }}
        
        /* 分组框样式 */
        QGroupBox {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_MEDIUM}px;
            margin-top: {self.current_theme.SPACING_MD}px;
            padding-top: {self.current_theme.SPACING_MD}px;
            font-weight: 600;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: {self.current_theme.SPACING_MD}px;
            padding: 0 {self.current_theme.SPACING_SM}px 0 {self.current_theme.SPACING_SM}px;
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {self.current_theme.SURFACE_ELEVATED};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {self.current_theme.BORDER};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        /* 进度条样式 */
        QProgressBar {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_SMALL}px;
            text-align: center;
            color: {self.current_theme.TEXT_PRIMARY};
        }}
        
        QProgressBar::chunk {{
            background-color: {self.current_theme.ACCENT_BLUE};
            border-radius: {self.current_theme.RADIUS_SMALL}px;
        }}
        
        /* 下拉框样式 */
        QComboBox {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_MEDIUM}px;
            padding: {self.current_theme.SPACING_SM}px;
            color: {self.current_theme.TEXT_PRIMARY};
        }}
        
        QComboBox:hover {{
            border-color: {self.current_theme.ACCENT_BLUE};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {self.current_theme.TEXT_SECONDARY};
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {self.current_theme.SURFACE_PRIMARY};
            border: 1px solid {self.current_theme.BORDER};
            border-radius: {self.current_theme.RADIUS_MEDIUM}px;
            selection-background-color: {self.current_theme.ACCENT_BLUE};
        }}
        """
    
    def create_animation(self, target, property_name: str, duration: int = 300) -> QPropertyAnimation:
        """创建动画"""
        animation = QPropertyAnimation(target, property_name.encode())
        animation.setDuration(duration)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        return animation


# 全局主题管理器实例
theme_manager = ThemeManager()
