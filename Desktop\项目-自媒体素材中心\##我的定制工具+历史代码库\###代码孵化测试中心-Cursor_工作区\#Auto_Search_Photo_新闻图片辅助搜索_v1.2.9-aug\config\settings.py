"""
应用配置管理
使用Pydantic进行类型安全的配置管理
"""

import os
from pathlib import Path
from typing import Optional, List, Dict, Any
from pydantic import BaseSettings, Field, validator
from loguru import logger


class UISettings(BaseSettings):
    """UI相关配置"""
    
    # 窗口设置
    window_width: int = Field(1400, description="窗口宽度")
    window_height: int = Field(1000, description="窗口高度")
    window_min_width: int = Field(1200, description="最小窗口宽度")
    window_min_height: int = Field(800, description="最小窗口高度")
    
    # 主题设置
    theme_name: str = Field("mac_dark", description="主题名称")
    enable_animations: bool = Field(True, description="启用动画效果")
    animation_duration: int = Field(300, description="动画持续时间(ms)")
    
    # 字体设置
    font_family: str = Field("SF Pro Display", description="主字体")
    font_size: int = Field(13, description="字体大小")
    font_size_small: int = Field(11, description="小字体大小")
    font_size_large: int = Field(16, description="大字体大小")
    
    class Config:
        env_prefix = "UI_"


class AISettings(BaseSettings):
    """AI相关配置"""
    
    # 模型设置
    default_model: str = Field("qwen2.5:7b", description="默认AI模型")
    model_timeout: int = Field(30, description="模型响应超时时间(秒)")
    max_retries: int = Field(3, description="最大重试次数")
    
    # API设置
    ollama_host: str = Field("http://localhost:11434", description="Ollama服务地址")
    
    class Config:
        env_prefix = "AI_"


class AutomationSettings(BaseSettings):
    """自动化相关配置"""
    
    # 操作设置
    default_speed: float = Field(1.0, description="默认播放速度")
    click_delay: float = Field(0.1, description="点击延迟(秒)")
    type_delay: float = Field(0.05, description="输入延迟(秒)")
    
    # 截图设置
    screenshot_quality: int = Field(95, description="截图质量(1-100)")
    screenshot_format: str = Field("PNG", description="截图格式")
    
    class Config:
        env_prefix = "AUTO_"


class FileSettings(BaseSettings):
    """文件管理相关配置"""
    
    # 路径设置
    download_dir: Optional[str] = Field(None, description="下载目录")
    temp_dir: Optional[str] = Field(None, description="临时目录")
    log_dir: Optional[str] = Field(None, description="日志目录")
    
    # 文件处理
    supported_image_formats: List[str] = Field(
        ["jpg", "jpeg", "png", "gif", "bmp", "webp"],
        description="支持的图片格式"
    )
    max_file_size_mb: int = Field(50, description="最大文件大小(MB)")
    
    @validator('download_dir', pre=True, always=True)
    def set_download_dir(cls, v):
        if v is None:
            return str(Path.home() / "Downloads")
        return v
    
    @validator('temp_dir', pre=True, always=True)
    def set_temp_dir(cls, v):
        if v is None:
            return str(Path.home() / ".ai_photo_search" / "temp")
        return v
    
    @validator('log_dir', pre=True, always=True)
    def set_log_dir(cls, v):
        if v is None:
            return str(Path.home() / ".ai_photo_search" / "logs")
        return v
    
    class Config:
        env_prefix = "FILE_"


class AppSettings(BaseSettings):
    """应用主配置"""
    
    # 应用信息
    app_name: str = Field("AI图片搜索助手 Pro", description="应用名称")
    app_version: str = Field("2.0.0", description="应用版本")
    app_author: str = Field("AI Assistant", description="应用作者")
    
    # 调试设置
    debug_mode: bool = Field(False, description="调试模式")
    log_level: str = Field("INFO", description="日志级别")
    
    # 子配置
    ui: UISettings = Field(default_factory=UISettings)
    ai: AISettings = Field(default_factory=AISettings)
    automation: AutomationSettings = Field(default_factory=AutomationSettings)
    file: FileSettings = Field(default_factory=FileSettings)
    
    class Config:
        env_prefix = "APP_"
        case_sensitive = False


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_file()
        self._settings: Optional[AppSettings] = None
        self._ensure_config_dir()
    
    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        config_dir = Path.home() / ".ai_photo_search"
        return str(config_dir / "config.json")
    
    def _ensure_config_dir(self):
        """确保配置目录存在"""
        config_dir = Path(self.config_file).parent
        config_dir.mkdir(parents=True, exist_ok=True)
    
    @property
    def settings(self) -> AppSettings:
        """获取配置设置"""
        if self._settings is None:
            self._settings = self.load_settings()
        return self._settings
    
    def load_settings(self) -> AppSettings:
        """加载配置设置"""
        try:
            if os.path.exists(self.config_file):
                logger.info(f"加载配置文件: {self.config_file}")
                return AppSettings.parse_file(self.config_file)
            else:
                logger.info("使用默认配置")
                settings = AppSettings()
                self.save_settings(settings)
                return settings
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            logger.info("使用默认配置")
            return AppSettings()
    
    def save_settings(self, settings: Optional[AppSettings] = None):
        """保存配置设置"""
        try:
            settings = settings or self._settings
            if settings:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    f.write(settings.json(indent=2, ensure_ascii=False))
                logger.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def update_setting(self, key_path: str, value: Any):
        """更新单个配置项"""
        try:
            keys = key_path.split('.')
            current = self.settings.dict()
            
            # 导航到目标位置
            for key in keys[:-1]:
                current = current[key]
            
            # 更新值
            current[keys[-1]] = value
            
            # 重新创建设置对象
            self._settings = AppSettings.parse_obj(self.settings.dict())
            self.save_settings()
            
            logger.info(f"配置已更新: {key_path} = {value}")
        except Exception as e:
            logger.error(f"更新配置失败: {e}")


# 全局配置管理器实例
config_manager = ConfigManager()
